import { boot } from 'quasar/wrappers'
import { Platform } from 'quasar'
import { isApp } from '../config'
import { safeStatusBar } from '../utils/capacitorUtils'

export let safeTop = ''

export function setSafeTop(value: string) {
  safeTop = value
}

export function updateSafeAreaTop() {
  // 直接在 :root (document.documentElement) 上设置CSS变量
  // 这样可以覆盖 app.scss 中的默认值
  document.documentElement.style.setProperty('--safe-area-top', safeTop)
}


export default boot(async () => {
  if (!isApp) {
    return
  }

  try {
    // 安全地导入 StatusBar
    const statusBarModule = await safeStatusBar()
    if (!statusBarModule) {
      console.warn('[StatusBar] StatusBar module not available')
      return
    }

    const { StatusBar, Style } = statusBarModule

    // 设置状态栏样式（可根据主题切换）
    await StatusBar.setStyle({ style: Style.Light }) // 或 Style.Dark

    // 设置状态栏背景色（建议与主题色一致）
    await StatusBar.setBackgroundColor({ color: '#027BE3' })

    // 关键：让WebView内容不被状态栏遮挡
    await StatusBar.setOverlaysWebView({ overlay: true })

    // 添加安全区域类
    document.body.classList.add('q-body--capacitor')
  } catch (err) {
    // 输出错误信息，便于调试
    // eslint-disable-next-line no-console
    console.error('[StatusBar] 初始化失败:', err)
  }
})
