import { boot } from 'quasar/wrappers'
import { Platform } from 'quasar'
import { isApp } from '../config'
import { safeStatusBar } from '../utils/capacitorUtils'
import { testStatusBarImport } from '../utils/statusBarTest'

export let safeTop = ''

export function setSafeTop(value: string) {
  safeTop = value
}

export function updateSafeAreaTop() {
  // 直接在 :root (document.documentElement) 上设置CSS变量
  // 这样可以覆盖 app.scss 中的默认值
  document.documentElement.style.setProperty('--safe-area-top', safeTop)
}


export default boot(async () => {
  console.log('[StatusBar] Boot function started')
  console.log('[StatusBar] isApp:', isApp)
  console.log('[StatusBar] Platform.is.capacitor:', Platform.is.capacitor)
  console.log('[StatusBar] Platform.is.cordova:', Platform.is.cordova)

  // 运行 StatusBar 导入测试
  await testStatusBarImport()

  if (!isApp) {
    console.log('[StatusBar] Not in app environment, skipping StatusBar initialization')
    return
  }

  try {
    console.log('[StatusBar] Attempting to load StatusBar module...')
    // 安全地导入 StatusBar
    const statusBarModule = await safeStatusBar()
    if (!statusBarModule) {
      console.warn('[StatusBar] StatusBar module not available')
      return
    }

    console.log('[StatusBar] StatusBar module loaded successfully')

    const { StatusBar, Style } = statusBarModule

    console.log('[StatusBar] Setting up StatusBar configuration...')

    // 设置状态栏样式（可根据主题切换）
    await StatusBar.setStyle({ style: Style.Light }) // 或 Style.Dark
    console.log('[StatusBar] Style set to Light')

    // 设置状态栏背景色（建议与主题色一致）
    await StatusBar.setBackgroundColor({ color: '#027BE3' })
    console.log('[StatusBar] Background color set to #027BE3')

    // 关键：让WebView内容不被状态栏遮挡
    await StatusBar.setOverlaysWebView({ overlay: true })
    console.log('[StatusBar] Overlay WebView set to true')

    // 添加安全区域类
    document.body.classList.add('q-body--capacitor')
    console.log('[StatusBar] Added q-body--capacitor class to body')

    console.log('[StatusBar] StatusBar initialization completed successfully')
  } catch (err) {
    // 输出错误信息，便于调试
    // eslint-disable-next-line no-console
    console.error('[StatusBar] 初始化失败:', err)
  }
})
