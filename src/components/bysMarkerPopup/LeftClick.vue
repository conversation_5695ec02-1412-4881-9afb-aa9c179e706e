<template>
  <q-card
    class="bysMarker-popup custom-mapbox-popup"
    :class="{ 'capture-image': is4gMarker && captureImageInfo }"
    flat
  >
    <q-card-section class="q-pa-none full-width">
      <q-item-label>{{ $t('form.unit') }}: {{ parentName }}</q-item-label>
      <q-item-label>{{ $t('form.boundaryID') }}: {{ MarkerHWID }}</q-item-label>
      <q-item-label>{{ $t('form.markerName') }}: {{ MarkerNo }}</q-item-label>
      <q-item-label>{{ $t('form.boundaryModel') }}: {{ MarkerModel }}</q-item-label>
      <q-item-label>{{ $t('form.boundaryType') }}: {{ markerTypeLabel }}</q-item-label>
      <template v-if="!is4gMarker">
        <q-item-label>{{ $t('form.parentController') }}: {{ controllerName }}</q-item-label>
        <q-item-label v-if="isAlarm && realControllerName !== controllerName">{{ $t('form.realParentController') }}:
          {{ realControllerName }}
        </q-item-label>
        <q-item-label v-if="cmdTime">{{ $t('form.lastReportTime') }}: {{ cmdTime }}</q-item-label>
      </template>
      <!-- 4g界桩状态数据 -->
      <template v-if="is4gMarker && (isAlarmReport || isInfoReport)">
        <q-item-label>{{ $t('CmdTest.batteryPower') }}: {{ battery }}v</q-item-label>
        <q-item-label>{{ $t('CmdTest.temperature') }}: {{ temp }}°</q-item-label>
        <q-item-label>{{ $t('CmdTest.humidity') }}: {{ humidity }}%</q-item-label>
        <q-item-label v-if="infoReportingState.length">
          {{ $t('form.status') }}: {{ infoReportingState.join(', ') }}
        </q-item-label>
        <q-item-label v-if="alarmStateLabels"> {{ alarmStateLabels }}</q-item-label>
        <q-item-label v-if="alarmOtherLabels.length"> {{ alarmOtherLabels.join(', ') }}</q-item-label>
        <q-item-label>{{ $t('CmdTest.lastCmd') }}: {{ getReportingTypeLabel }}</q-item-label>
        <q-item-label v-if="lastCmdTime">{{ $t('form.lastReportTime') }}: {{ lastCmdTime }}</q-item-label>
      </template>
      <template v-if="is4gMarker && !isAlarmReport && !isInfoReport">
        <q-item-label>{{ $t('CmdTest.lastCmd') }}: {{ $t('CmdTest.NoCmdYet') }}</q-item-label>
      </template>
      <!-- 只有报警时才显示抓拍的照片 -->
      <template v-if="is4gMarker && isAlarmReport && captureImageInfo">
        <q-item-label v-if="captureImageInfo.rid === RID">
          <q-img
            :src="captureImageInfo.dataUrl"
            :ratio="16/9"
            spinner-color="primary"
            spinner-size="2rem"
            fit="contain"
            class="cursor-pointer min-w-16 max-w-24"
            loading="lazy"
            @click.stop.prevent="previewImageWithLightbox(captureImageInfo.dataUrl)"
          >
            <q-tooltip>
              {{ $t('historyTable.clickToViewFullImage') }}
            </q-tooltip>
          </q-img>
        </q-item-label>
      </template>
    </q-card-section>

    <q-card-actions class="q-pa-none full-width q-pt-md">
      <q-btn-group
        flat
        spread
        class='custom-mapbox-popup-btn-group full-width'
      >
        <q-btn
          v-if="hasMarkerEditPerm"
          size='sm'
          color="primary"
          @click="quickEdit"
          :label="$t('common.dataEdit')"
        />
        <q-btn
          v-if='isAlarm'
          size='sm'
          color="primary"
          @click="rmAlarm"
          :label="$t('CmdTest.closeAlarm')"
        />
        <q-btn
          size='sm'
          color="primary"
          @click="openMediaInfo"
          :label="$t('common.mediaData')"
        />
        <q-btn
          size='sm'
          color="primary"
          @click="wakeUpBaiduMap"
          :label="$t('CmdTest.goto')"
        />
        <q-btn-dropdown
          size='sm'
          color="primary"
          :label="$t('common.more')"
          dropdown-icon="expand_more"
          auto-close
          class="q-px-sm"
          v-if="hasMarkerHistoryMenuPerm || hasMarkerPatrolHistoryMenuPerm"
        >
          <q-list dense>
            <q-item v-if="hasMarkerHistoryMenuPerm" clickable v-close-popup @click="openMarkerHistory">
              <q-item-section>{{ $t('menus.markerHistory') }}</q-item-section>
            </q-item>
            <q-item v-if="hasMarkerPatrolHistoryMenuPerm" clickable v-close-popup @click="openNFCHistory">
              <q-item-section>{{ $t('menus.MarkerNFCPatrolHistory') }}</q-item-section>
            </q-item>
          </q-list>
        </q-btn-dropdown>
      </q-btn-group>
    </q-card-actions>

    <teleport to="body">
      <lightbox
        class="upload-image-preview-lightbox"
        :visible="visibleLightbox"
        :imgs="lightboxUrl"
        @hide="onHideLightbox"
      >
      </lightbox>
    </teleport>
  </q-card>
</template>

<script lang="ts">
  import { StrPubSub } from 'ypubsub'
  import { BysMarker, Controller } from '@services/dataStore'
  import { bysdb } from '@ygen/bysdb'
  import { wrapperInvalidDate } from '@utils/dayjs'
  import {
    BysMarkerDumpingAlarm,
    BysMarkerForceUpdate,
    MarkerPhotographUpload,
    OpenMenu,
    RemoveBysMarkerAlarm,
    removeBysMarkerAlarmCmd,
    WakeupBaiduMap,
  } from '@utils/pubSubSubject'
  import {
    checkBysMarkerIsAlarm, checkIs4GMarker,
    CmdCode,
    decode4gMarkerAlarmState,
    decode4gMarkerInfoReportState,
    MarkerType
  } from '@utils/common'
  import { GET_DATA, GET_DATA_BY_INDEX, NS } from '@store/data/methodTypes'
  import { DataName } from '@src/store'
  import { getGCJ02LngLat } from '@utils/gcoord'
  import { PermissionType } from '@utils/permission'
  import { BysMarkerAndUpdateInfo, ICustData } from '@utils/bysdb.type'
  import { bysproto } from '@ygen/controller'
  import { defineComponent } from 'vue'
  import Lightbox from 'vue-easy-lightbox'
   import { user as userPermission } from '@ygen/userPermission'

  interface ICaptureImageInfo {
    rid: string
    dataUrl: string
    custData: ICustData
    camImageData: bysdb.ICamImageData
  }

  // RID -> ICaptureImageInfo
  const CaptureImageCache = new Map<string, ICaptureImageInfo>()

  export default defineComponent({
    name: 'BysMarkerPopupLeftClick',
    components: { Lightbox },
    props: {
      RID: {
        type: String,
        required: true,
      },
    },
    data() {
      return {
        data: undefined as BysMarkerAndUpdateInfo | undefined,
        captureImageInfo: undefined as ICaptureImageInfo | undefined,

        // 图片预览显示控制
        visibleLightbox: false,
        lightboxUrl: '',
      }
    },
    methods: {
      quickEdit() {
        this.updateMarkerData(this.RID)
        StrPubSub.publish(OpenMenu, 'BysMarkerQuickEdit', (vm) => {
          if (!this.data.MarkerType) {
            // this.data.MarkerType = 0
            this.data = Object.assign({}, this.data, {
              MarkerType: 0
            })
          }
          vm.markerData = { ...this.data }
          vm.isVisible = true
          vm.updateRowData = true
        })
      },
      openMediaInfo() {
        StrPubSub.publish(OpenMenu, 'MediaInfo', (vm) => {
          vm.HWIDFilter = this.MarkerHWID
          vm.HWIDIsMarker = true
          vm.currentRow.MarkerRID = this.data.RID
          vm.isBysMarker = true
          vm.isController = false
        })
      },
      rmAlarm() {
        StrPubSub.publish(removeBysMarkerAlarmCmd, this.data, this.controller)
      },
      wakeUpBaiduMap() {
        if (!this.data) return;
        let item = {
          lonlat: this.data.Lon + ' ' + this.data.Lat,
          name: this.data.MarkerNo,
        }
        StrPubSub.publish(WakeupBaiduMap, item)
      },
      openMarkerHistory() {
        if (!this.data) return;
        StrPubSub.publish(OpenMenu, 'MarkerHistory', (vm: any) => {
          vm.markerRID = this.data?.RID;
          vm.isVisible = true;
          // 要执行一次markerChange来赋值searchMarkerType
          vm.markerChange(this.data?.RID)
        });
      },
      openNFCHistory() {
        if (!this.data) return;
        StrPubSub.publish(OpenMenu, 'MarkerNFCPatrolHistory', (vm: any) => {
          vm.markerRID = this.data?.RID;
          vm.isVisible = true;
        });
      },
      getJudgeRet(data) {
        return data ? this.$t('common.yes') : this.$t('common.no')
      },
      getData(key: string): bysdb.IDbBysMarker | undefined {
        return BysMarker.getData(key) as bysdb.IDbBysMarker | undefined
      },
      markerDataChanged(rid: string) {
        if (!this.data?.rid) {
          return
        }
        if (this.data.rid !== rid) {
          return
        }
        this.updateMarkerData(rid)
      },
      updateMarkerData(rid: string) {
        this.data = this.getData(rid)
      },
      //收到解除报警
      alarmRemoved(bysMarker: bysdb.IDbBysMarker) {
        this.markerDataChanged(bysMarker.RID ?? '')
        this.captureImageInfo = undefined
      },
      // 收到报警事件，更新数据源
      bysMarkerAlarm(bysMarker: bysdb.IDbBysMarker) {
        this.markerDataChanged(bysMarker.RID ?? '')
      },
      cacheImage(rid: string, data: ICaptureImageInfo) {
        if (CaptureImageCache.has(rid)) {
          const cache = CaptureImageCache.get(rid)
          URL.revokeObjectURL(cache!.dataUrl)
        }
        CaptureImageCache.set(rid, data)
      },
      // 接收界桩上传的图片，显示到信息列表中
      markerCaptureImageHandler(dbMarker: bysdb.IDbBysMarker, imageBytes: Uint8Array, camImageData: bysdb.ICamImageData, custData: ICustData) {
        // 先缓存上传的图片数据，即使不是当前界桩，以便切换时显示
        const bodyBlob = new Blob([imageBytes], { type: 'image/jpg' })
        const dataUrl = URL.createObjectURL(bodyBlob)
        const markerRid = dbMarker.RID as string
        const data: ICaptureImageInfo = {
          rid: markerRid,
          dataUrl,
          camImageData,
          custData,
        }
        this.cacheImage(markerRid, data)

        // 不是当前界桩上传的
        if (this.RID !== dbMarker.RID) return

        this.captureImageInfo = data
      },
      previewImageWithLightbox(url: string) {
        if (!url) {
          return
        }
        this.lightboxUrl = url
        this.visibleLightbox = true
      },
      onHideLightbox() {
        this.lightboxUrl = ''
        this.visibleLightbox = false
      },
    },
    computed: {
      ControllerType() {
        return {
          1: this.$t('CmdTest.repeater'),
          2: this.$t('CmdTest.baseStation'),
        }
      },
      infoReportingType() {
        const reportLabels = {
          1: this.$t('CmdTest.reportOnStartup'),
          2: this.$t('CmdTest.reportOnDebugging'),
          3: this.$t('CmdTest.reportOnRegular'),
        }
        return `${this.$t('CmdTest.report')} (${reportLabels[this.infoReporting?.type]})`
      },
      alarmReportingType() {
        const reportLabels = {
          1: this.$t('CmdTest.reportOnStartup'),
          2: this.$t('CmdTest.reportOnDebugging'),
          3: this.$t('CmdTest.reportToAlarm')
        }
        return `${this.$t('CmdTest.alarm')} (${reportLabels[this.alarmReporting?.type]})`
      },
      getReportingTypeLabel() {
        if (this.isInfoReport) {
          return this.infoReportingType
        }

        if (this.isAlarmReport) {
          return this.alarmReportingType
        }

        return `${this.$t('CmdTest.unknown')} (${(this.alarmReporting ?? this.infoReporting)?.type})`
      },
      battery() {
        const battery = this.infoReporting?.battery ?? 0
        return +(battery / 1000).toFixed(1)
      },
      temp() {
        return this.infoReporting?.temp?.toFixed(1) ?? 0
      },
      humidity() {
        return this.infoReporting?.rh?.toFixed(1) ?? 0
      },
      alarmOtherLabels() {
        const labels: string[] = []
        const { attitude, vibration } = this.alarmReporting ?? {}
        if (attitude) {
          labels.push(this.$t('CmdTest.tiltAngleVal', { value: attitude }))
        }
        // 震动信息
        if (vibration) {
          const { amplitude, duration } = vibration
          if (amplitude) labels.push(this.$t('CmdTest.vibrationAmplitudeVal', { value: amplitude }))
          if (duration) labels.push(this.$t('CmdTest.vibrationDurationVal', { value: duration }))
        }

        return labels
      },
      alarmStateLabels() {
        const { alarmstate } = this.alarmReporting ?? this.infoReporting ?? {}
        // 不存在，或者正常参数0值，也不返回异常文本
        if (!alarmstate) {
          return ''
        }

        const alarmState = decode4gMarkerAlarmState(alarmstate)
        const alarmStateLabels: string[] = []
        if (alarmState.displacementAlarm) {
          alarmStateLabels.push(this.$t('CmdTest.displacementAlarm'))
        }
        if (alarmState.vibrationAlarm) {
          alarmStateLabels.push(this.$t('CmdTest.vibrationAlarm'))
        }
        if (alarmState.tiltAlarm) {
          alarmStateLabels.push(this.$t('CmdTest.tiltAlarm'))
        }
        if (alarmState.infraredAlarm) {
          alarmStateLabels.push(this.$t('CmdTest.infraredAlarm'))
        }
        return this.$t('CmdTest.alarmStatus', { status: alarmStateLabels.join(', ') })
      },
      infoReportingState() {
        const { state } = this.infoReporting ?? {}
        if (!state) return []

        const stateMode = decode4gMarkerInfoReportState(state)
        const labelList: string[] = []
        // 只显示异常的状态文本
        if (stateMode.battery) {
          labelList.push(this.$t('CmdTest.batteryStatus', { status: this.$t('CmdTest.abnormal') }))
        }
        if (stateMode.rtcClock) {
          labelList.push(this.$t('CmdTest.rtcClockStatus', { status: this.$t('CmdTest.abnormal') }))
        }
        if (stateMode.gpsModule) {
          labelList.push(this.$t('CmdTest.gpsModuleStatus', { status: this.$t('CmdTest.abnormal') }))
        }
        if (stateMode.alarmLock) {
          labelList.push(this.$t('CmdTest.alarmLock'))
        }
        if (stateMode.camera) {
          labelList.push(this.$t('CmdTest.camera', { status: this.$t('CmdTest.abnormal') }))
        }
        if (stateMode.infraredProbe) {
          labelList.push(this.$t('CmdTest.infraredProbe', { status: this.$t('CmdTest.abnormal') }))
        }

        return labelList
      },
      lastCmdTime() {
        const { MarkerCmdTime } = this.data?.markerInfo ?? {}
        return MarkerCmdTime ? wrapperInvalidDate(MarkerCmdTime) : ''
      },
      markerUploadCmd() {
        const { MarkerUploadCmd } = this.data?.markerInfo ?? {}
        return MarkerUploadCmd
      },
      infoReporting(): bysproto.IBInfoReporting | undefined {
        const { InfoReporting } = this.data?.markerInfo ?? {}
        return InfoReporting
      },
      isInfoReport() {
        return this.markerUploadCmd === CmdCode.InfoReport && !!this.infoReporting
      },
      alarmReporting(): bysproto.IBAlarmReporting | undefined {
        const { AlarmReporting } = this.data?.markerInfo ?? {}
        return AlarmReporting
      },
      isAlarmReport() {
        return this.markerUploadCmd === CmdCode.AlarmReport && !!this.alarmReporting
      },
      MarkerTypeOptions() {
        return [
          { label: this.$t('form.normalMarkers'), value: MarkerType.Regular },
          { label: this.$t('form.4GMarkers'), value: MarkerType.Net4G },
          { label: this.$t('form.4GMarkersPro'), value: MarkerType.Net4GPro },
        ]
      },
      markerTypeLabel() {
        return this.MarkerTypeOptions.find(opt => opt.value === this.data?.MarkerType)?.label ?? this.data?.MarkerType ?? ''
      },
      is4gMarker() {
        return checkIs4GMarker(this.data?.MarkerType)
      },
      permissions(): Array<userPermission.IDbPermission> {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Permission) ?? []
      },
      markerMenuPerm() {
        return this.permissions.find(v => v.PermissionType === PermissionType.menu && v.PermissionName === 'Data.DbBysMarker')
      },
      // 判断是否有界桩编辑权限
      hasMarkerEditPerm() {
        return !!this.markerMenuPerm
      },
      // 判断是否有界桩NFC巡查历史菜单权限
      hasMarkerPatrolHistoryMenuPerm() {
        return this.permissions.some(v => v.PermissionType === PermissionType.menu && v.PermissionName === 'Query.DbMarkerPatrolHistory')
      },
      // 判断是否有界桩历史菜单权限
      hasMarkerHistoryMenuPerm(){
        return this.permissions.some(v => v.PermissionType === PermissionType.menu && v.PermissionName === 'Query.DbMarkerHistory')
      },
      cmdTime() {
        return this.data?.updateInfo?.CmdTime ? wrapperInvalidDate(this.data?.updateInfo?.CmdTime) : ''
      },
      dataLon() {
        return this.data?.Lon ?? ''
      },
      dataLat() {
        return this.data?.Lat ?? ''
      },
      updateInfo() {
        return this.data?.updateInfo
      },
      StatusInfo() {
        return this.updateInfo?.StatusInfo ?? {}
      },
      GPS() {
        return this.updateInfo.GPS ?? {}
      },
      isAlarm() {
        return checkBysMarkerIsAlarm(this.data as BysMarkerAndUpdateInfo)
      },
      MarkerNo() {
        return this.data?.MarkerNo ?? ''
      },
      MarkerHWID() {
        return this.data?.MarkerHWID ?? ''
      },
      MarkerModel() {
        return this.data?.MarkerModel === 1 ? this.$t('form.long') : this.$t('form.short')
      },
      parentName() {
        return BysMarker.getParentName(this.data?.OrgRID)
      },
      controllerName() {
        const controller = Controller.getData(this.data?.ControllerRID) as bysdb.IDbController | undefined
        return controller?.ControllerNo ?? ''
      },
      realControllerName() {
        const controller = this.$store.getters[`${NS}/${GET_DATA_BY_INDEX}`](DataName.Controller,
          this.updateInfo?.StationID,
        )
        return controller?.ControllerNo ?? ''
      },
      controllerType() {
        const controller = Controller.getData(this.data?.ControllerRID) as bysdb.IDbController | undefined
        return this.ControllerType[controller?.ControllerType as number]
      },
      controller() {
        return Controller.getData(this.data?.ControllerRID) as bysdb.IDbController | undefined
      },
      controllerHWID() {
        const controller = Controller.getData(this.data?.ControllerRID) as bysdb.IDbController | undefined
        return controller?.ControllerHWID
      },
      coordinate() {
        return getGCJ02LngLat(this.data ?? {}).join(',')
      },
      deviceFieldStrength() {
        return this.updateInfo?.DeviceFieldStrength + 'dBm'
      },
    },
    watch: {
      RID: {
        immediate: true,
        handler(val) {
          this.updateMarkerData(val)

          // 查找界桩可能存在的上报的抓拍照片
          if (CaptureImageCache.has(val)) {
            this.captureImageInfo = CaptureImageCache.get(val)
          } else {
            this.captureImageInfo = undefined
          }
        },
      },
    },
    beforeUnmount(): void {
      // 解绑组件上所有自定义事件
      StrPubSub.unsubscribe(BysMarkerDumpingAlarm, this.bysMarkerAlarm)
      StrPubSub.unsubscribe(RemoveBysMarkerAlarm, this.alarmRemoved)
      StrPubSub.unsubscribe(BysMarkerForceUpdate, this.markerDataChanged)
      StrPubSub.unsubscribe(MarkerPhotographUpload, this.markerCaptureImageHandler)
    },
    mounted(): void {
      //订阅解除报警消息，重置界面
      StrPubSub.subscribe(BysMarkerDumpingAlarm, this.bysMarkerAlarm)
      StrPubSub.subscribe(RemoveBysMarkerAlarm, this.alarmRemoved)
      StrPubSub.subscribe(BysMarkerForceUpdate, this.markerDataChanged)
      StrPubSub.subscribe(MarkerPhotographUpload, this.markerCaptureImageHandler)
    },
  })
</script>

<style lang="scss">
  @import "src/css/map.popup";

  .vel-modal.upload-image-preview-lightbox {
    @apply bg-slate-600;
    top: var(--safe-area-top) !important;
  }
</style>
