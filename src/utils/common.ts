import { i18n } from '@boot/i18n'
import { bysdb } from '@ygen/bysdb'
import { StrPubSub } from 'ypubsub'
import {
  BysMarkerDumpingAlarm,
  ControllerHeartbeat,
  ControllerOffline,
  ControllerOfflinePanic,
  ControllerOnline,
  ControllerStateChanged,
  InsertDbController,
  MarkerHasSomeAbnormal,
  QueryControllerFinish,
  QueryMediaInfoFinish,
  UpdateDbController,
  UpdateMarkerForm,
} from './pubSubSubject'
import { BysMarker, Controller, Permission, Role, Unit, User } from '@services/dataStore'
import { GET_DATA, NS } from '@store/data/methodTypes'
import { DataName } from '@store/data'
import { Store } from '@src/store'
import { Dialog, Notify, Platform, QDialogOptions } from 'quasar'
import { DefUuid } from '@src/config'
import { BysMarkerAndUpdateInfo, ControllerAndUpdateCmd, ControllerCustomError } from './bysdb.type'
import log from '@utils/log'
import { bysproto } from '@ygen/controller'
import { doc, yrpcmsg } from '@ygen/bys.api'
import { dayjs, getDeffDay, getSubtractTime, toLocalTime, utcTime } from './dayjs'
import { crud } from '@ygen/org.rpc'
import { ICallOption, TrpcMeta } from 'jsyrpc'
import { PrpcDbBysMarker } from '@ygen/bysdb.rpc.yrpc'
import IMarkerInfo = doc.IMarkerInfo;

// import log from 'loglevel'

export function sleep(duration: number): Promise<any> {
  return new Promise(resolve => {
    window.setTimeout(() => {
      resolve(true)
    }, duration)
  })
}

export enum dataEditStatus {
  show,
  add,
  edit
}

export interface DeviceStatus {
  isAlarm?: boolean
  isRegister?: boolean
  isTest?: boolean
  isValidPosition?: boolean
  isLowPowerAlarm?: boolean
  isDumping?: boolean

  // 0xd1 电量位参数值
  batteryPower?: string
}

/*界桩状态：1个字节，例：0x6F。
* 位序	界桩当前状态
7	1/0	1=已为报警状态/0=已为监控状态（已解除报警）
6	1/0	1=已入网注册/配置/同步校时/0=未入网注册/搜索连接
5	1/0	1=测试状态/0=监控工作状态
4	1/0	1=GPS有效新位/0=GPS无效旧位
3	1/0	1=三轴倾倒异常/0=三轴无倾倒异常
2	1/0	电池电量_位2
1	1/0	电池电量_位1
0	1/0	电池电量_位0（0-F=16个电池电量级显示） 0级=小于低电报警门限值

* */
export const DeviceStatus = {
  /** 前4位(4-7位)状态为通用状态 **/
  isAlarm(status: number): boolean {
    return (status & 0x80) >>> 7 === 1
  },
  isRegister(status: number): boolean {
    return (status & 0x40) >>> 6 === 1
  },
  isTest(status: number): boolean {
    return (status & 0x20) >>> 5 === 1
  },
  isValidPosition(status: number): boolean {
    return (status & 0x10) >>> 4 === 1
  },
  isDumping(status: number): boolean {
    return (status & 0x08) >>> 3 === 1
  },

  // 正常状态下低4位(0-3位)为电量数据
  batteryPower(status: number): string {
    return (status & 0x07).toString(16).toUpperCase()
  },
  isLowPowerAlarm(status: number): boolean {
    return (status & 0x07) === 0
  },
}

export function decodeDeviceStatus(status: number): DeviceStatus {
  const result: DeviceStatus = {}
  Object.keys(DeviceStatus).forEach(key => {
    result[key] = DeviceStatus[key]?.(status) ?? false
  })

  return result
}

export interface Marker4gAlarmState {
  // Bit0：位移报警(0:正常，1:报警)
  displacementAlarm: boolean
  // Bit1：震动报警(0:正常，1:报警)
  vibrationAlarm: boolean
  // Bit2：倾斜报警(0:正常，1:报警)
  tiltAlarm: boolean
  // Bit3：红外报警(0:正常，1:报警)
  infraredAlarm: boolean
  // Bit4-Bit31：预留
}

// 4g界桩报警状态解码
export function decode4gMarkerAlarmState(alarmState: number): Marker4gAlarmState {
  return {
    displacementAlarm: (alarmState & 0x01) === 1,
    vibrationAlarm: ((alarmState >> 1) & 0x01) === 1,
    tiltAlarm: ((alarmState >> 2) & 0x01) === 1,
    infraredAlarm: ((alarmState >> 3) & 0x01) === 1,
  }
}

export interface Marker4gInfoReportState {
  // Bit0：报警锁定(0:正常，1:报警锁定)
  alarmLock: boolean
  // Bit1：RTC时钟状态(0:正常，1:故障)
  rtcClock: boolean
  // Bit2：GPS模块状态(0:正常，1:故障)
  gpsModule: boolean
  // Bit3：三轴传感器状态(0:正常，1:故障)
  axis3Sensor: boolean
  // Bit4：电池状态(0:正常，1:故障)
  battery: boolean
  // Bit5：摄像头状态(0:正常，1:故障)(预留)
  camera: boolean
  // Bit6：红外探头状态(0:正常，1:故障)(预留)
  infraredProbe: boolean
  // Bit7-Bit31：预留
}

export function decode4gMarkerInfoReportState(state: number): Marker4gInfoReportState {
  return {
    alarmLock: (state & 0x01) === 1,
    rtcClock: ((state >> 1) & 0x01) === 1,
    gpsModule: ((state >> 2) & 0x01) === 1,
    axis3Sensor: ((state >> 3) & 0x01) === 1,
    battery: ((state >> 4) & 0x01) === 1,
    camera: ((state >> 5) & 0x01) === 1,
    infraredProbe: ((state >> 6) & 0x01) === 1,
  }
}

// 设备类型
export enum SystemDeviceTypes {
  Server = 0x00,
  Device,
  BaseStation,
  Repeater,
  All = 0xFF,
}

// 指令码
export enum CmdCode {
  // 系统下发/应答指令
  D0 = 0xD0,
  // 打卡指令
  D1,
  // 报警指令
  D2,
  // test cmd
  D3,

  // 4g界桩的命令
  InfoReport = 21,
  AlarmReport = 22
}

// 界桩类型枚举
export enum MarkerType {
  Regular,
  Net4G,
  Net4GPro,
}

/**
 * 检查界桩是否为4G类型（包括Net4G和Net4GPro）
 * @param markerType 界桩类型
 * @returns 是否为4G类型界桩
 */
export function checkIs4GMarker(markerType: number | null | undefined): boolean {
  return markerType === MarkerType.Net4G || markerType === MarkerType.Net4GPro
}

export enum ControllerDeviceType {
  FSK,
  Net4g,
}

// 编码url对象参数，转成k1=v1&k2=v2...字符串
export function encodeUrlQuery(data: { [key: string]: any }): string {
  const queryParams = new URLSearchParams()
  const keys = Object.keys(data)
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i]
    const val = data[key]
    queryParams.append(key, JSON.stringify(val))
  }

  return queryParams.toString()
}

//marker
export function calcMarkerPower(batteryPower: string): string {
  let pow = parseInt(batteryPower)
  if (isNaN(pow) || pow === 0) {
    return '0%'
  }

  // 因为界桩的电池有电容保护的原因，指令的电量等级会出现不准确，本地调整电量百分比
  if (pow >= 5) {
    pow = 7
  } else if (pow >= 3) {
    pow = 6
  } else if (pow === 2) {
    pow = 5
  } else if (pow === 1) {
    pow = 3
  }

  // 整数不保留小数点
  const val = pow / 7 * 100
  if (val === Math.floor(val)) {
    return val + '%'
  }

  return (val.toFixed(1) + '%')
}

//controller
export function calcControllerPower(pw = 0): string {
  if (isNaN(pw)) {
    return '0V'
  }
  const power = pw.toFixed(1)
  return power ? `${power} V` : '0V'
}

export function queryAllControllerData(): bysdb.IDbController[] {
  return Store.getters[`${NS}/${GET_DATA}`](DataName.Controller)
}

//基站/中继信道号   ChannelNo
export const ControllerTypes = {
  baseController: 2,
  relayController: 1,
}

export enum NetworkTypeEnum {
  Unknown,
  Wired,
  FourG,
  Fsk,
}

//网络类型 0:未知 1:有线网络， 2:4g网络  3:fsk
export const exNetworkType = {
  0: () => i18n.global.t('CmdTest.unknown'),
  1: () => i18n.global.t('CmdTest.wired'),
  2: () => i18n.global.t('CmdTest.fourG'),
  3: () => i18n.global.t('form.fsk'),
}

//维修权限字段
export const Maintain = 'Sys.Maintain'
export const CrudLogQuery = 'DbCrudLog.Query'

//校验是否有维修权限
export function isHaveMaintainPerm(): boolean {
  return !!Permission.getDataByIndex(Maintain)
}

export const ControllerLowPower = 5.8  //5.8伏特
export const LatestUploadTime = 10 //10s内的ptt有效
// 4g界桩低电压阀值，单位mv，采用锂电池，工作电压一般在3~4.2v
export const MarkerLowPower4g = 3200

export enum ControllerAbnormalReason {
  LowPower = 'low-power',
  NetworkType = 'network-type',
  NotFskButNotOnline = 'not-fsk-but-not-online',
  BaseStationNotOnline = 'base-station-not-online',
  TimeError = 'time-error',
}

/***
 * 用于检测控制器是否异常
 */
function checkControllerError(data: ControllerAndUpdateCmd) {
  //如果用户没有权限，则直接设置Error为false
  if (!isHaveMaintainPerm()) {
    Controller.setPartData(data.RID as string, {
      Error: {
        err: false,
        status: [],
      },
    } as ControllerCustomError)
    return
  }

  let controller = Controller.getData(data.RID as string) as ControllerAndUpdateCmd
  if (!controller) {
    return
  }

  // 设置默认参数,利用Set对象去重
  let err = false
  const status = new Set<string>()

  //判断控制器电量
  if (data.controllerState && (!data.controllerState?.Power || data.controllerState?.Power <= ControllerLowPower)) {
    err = true
    status.add(ControllerAbnormalReason.LowPower)
  }

  if (data.DefaultNetworkType !== data.controllerState?.NetworkType) {
    err = true
    status.add(ControllerAbnormalReason.NetworkType)
  }

  //判断控制器的类型
  if (data.ControllerType === ControllerTypes.relayController) {
    //中继
    //FSK = 3
    if (data.DefaultNetworkType !== 3 && !data.controllerState?.online) {
      err = true
      status.add(ControllerAbnormalReason.NotFskButNotOnline)
    }
  } else {
    //基站
    if (!data.controllerState?.online) {
      err = true
      status.add(ControllerAbnormalReason.BaseStationNotOnline)
    }
  }

  // 时间异常报警
  if (data.controllerState?.status?.timeError) {
    err = true
    status.add(ControllerAbnormalReason.TimeError)
  }

  const Error: ControllerCustomError = { err, status: Array.from(status) }
  Controller.setPartData(controller.RID as string, { Error })
}

export function checkOneControllerError(data: ControllerAndUpdateCmd) {
  checkControllerError(data)
  StrPubSub.publish(ControllerStateChanged, data)
}

/***
 * 用于检测所有控制器是否异常
 */
export function checkAllControllerError() {
  const allController: Array<ControllerAndUpdateCmd> = queryAllControllerData()
  for (const item of allController) {
    checkControllerError(item)
  }
}

StrPubSub.subscribe(ControllerOnline, (data: ControllerAndUpdateCmd | bysdb.IDbBysMarker, loginReq: bysproto.IBloginReq) => {
  if (loginReq.DeviceType === ControllerDeviceType.Net4g) return
  checkOneControllerError(data)
})
StrPubSub.subscribe(ControllerOfflinePanic, checkOneControllerError)
StrPubSub.subscribe(ControllerOffline, (data: ControllerAndUpdateCmd | bysdb.IDbBysMarker, loginReq: bysproto.IBloginReq) => {
  if (loginReq.DeviceType === ControllerDeviceType.Net4g) return
  checkOneControllerError(data)
})
StrPubSub.subscribe(ControllerHeartbeat, checkOneControllerError)
StrPubSub.subscribe(UpdateDbController, checkOneControllerError)

StrPubSub.subscribe(QueryMediaInfoFinish, () => {
  setTimeout(checkAllControllerError)
})

export function isDisMarkerAlarmPopupWind() {
  return !Store.state.Settings.closePopupWind
}

//上级控制器HWID << 8 | 上级控制器对应的通道号  -- > 中继的RID
export const controllerNoMap = {}
let allControllers: bysdb.IDbController[] = []

function getParHWIDByRID(rid: string): number {
  return allControllers.find(item => item.RID === rid)?.ControllerHWID ?? -1
}

/*function delFromChildControllerMap(data: bysdb.IDbController) {
  controllerNoMap.delete(createKeyVal(data.ParentRID, data.ParentChannelNo))
}*/

/***
 * 创建par-child key （getParHWIDByRID(ParentRID) << 8 | (ParentChannelNo as number)）
 * @param ParentRID
 * @param ParentChannelNo === StationDeviceNo
 * @param stationID
 */
export function createKeyVal(ParentRID, ParentChannelNo, stationID = -1) {
  //左移8位+no
  return (stationID === -1 ? getParHWIDByRID(ParentRID) : stationID) << 8 | (ParentChannelNo as number)
}

function insertIntoChildControllerMap(data: bysdb.IDbController) {
  controllerNoMap[createKeyVal(data.ParentRID, data.ParentChannelNo)] = data.RID
}

function updateChildControllerMap(newData: bysdb.IDbController) {
  insertIntoChildControllerMap(newData)
}

function initChildControllerMap() {
  allControllers = Controller.getDataList()
  //获取所有的中继
  const allRelayControllers = allControllers.filter(
    item => item.ControllerType === ControllerTypes.relayController)
  for (const item of allRelayControllers) {
    controllerNoMap[createKeyVal(item.ParentRID, item.ParentChannelNo)] = item.RID
  }
}

//订阅控制器查询，控制器增加，生成map (stationNo + stationDevNo --- controllerRID)
StrPubSub.subscribe(QueryControllerFinish, initChildControllerMap)
StrPubSub.subscribe(InsertDbController, insertIntoChildControllerMap)
StrPubSub.subscribe(UpdateDbController, updateChildControllerMap)

export function outputDBError(keysObj, err: string): string {
  const keys = Object.keys(keysObj)
  let error = ''
  for (let key of keys) {
    if (err.includes(key)) {
      error = keysObj[key]
      break
    }
  }
  return error || err
}

//内置属性翻译
export const BuiltInAttrs = ['root', 'admin', 'data.manager', 'tourist', 'maintain', 'dev.history', 'opt.history', 'nfc']

export function BuiltInAttrTranslate() {
  return {
    'admin': i18n.global.t('builtInAttr.admin'),//数据查阅
    'data.manager': i18n.global.t('builtInAttr.dataManager'),//数据管理
    'tourist': i18n.global.t('builtInAttr.tourist'),//菜单管理
    'maintain': i18n.global.t('builtInAttr.maintain'),//维护
    'dev.history': i18n.global.t('builtInAttr.devHis'),//设备历史
    'opt.history': i18n.global.t('builtInAttr.optHis'),//操作历史
    'root': i18n.global.t('builtInAttr.root'),//默认
    'nfc': i18n.global.t('builtInAttr.nfcManager'), // NFC
  }
}

// cordova.plugins.notification插件通知消息配置参数部分常用节选
export interface LocalNotificationMessageOptions {
  // 唯一标识，没有会自动创建
  id?: number
  // 标题，默认为App名称
  title?: string
  // 消息内容
  text?: string | Array<Record<string, any> & { message: string, person?: string }>
  // 振动
  vibrate?: boolean
  // 前景显示
  foreground?: boolean
  // 操作按钮组
  actions?: Array<Record<string, any>>
  // 进度条
  progressBar?: Record<string, any> & { value: number }
  // 通知顶部app名称前的图标
  smallIcon?: string
  // 通知内容图标，默认在右侧
  icon?: string
  // 消息底部图片，当前只有第图片有效
  attachments?: Array<string>
  // 通知分组
  group?: string
}

function useLocalNotificationId(): () => number {
  let localNotificationId: number = Math.floor(Math.random() * 1000)
  return () => {
    if (localNotificationId >= Number.MAX_SAFE_INTEGER) {
      localNotificationId = 0
    }

    return localNotificationId++
  }
}

const nextLocalNotificationId = useLocalNotificationId()

export async function scheduleLocalNotify(msg: LocalNotificationMessageOptions) {
  try {
    // 使用 Capacitor LocalNotifications
    const { LocalNotifications } = await import('@capacitor/local-notifications')

    await LocalNotifications.schedule({
      notifications: [{
        id: msg.id ?? nextLocalNotificationId(),
        title: msg.title || '',
        body: msg.text || '',
        schedule: { at: new Date(Date.now() + 100) }, // 立即显示
        sound: undefined, // 使用默认声音
        attachments: undefined,
        actionTypeId: '',
        extra: null
      }]
    })
  } catch (error) {
    log.warn('LocalNotifications failed, falling back to cordova:', error)
    // 降级到 Cordova 插件
    try {
      cordova.plugins.notification.local.schedule({
        id: msg.id ?? nextLocalNotificationId(),
        ...msg,
      })
    } catch (cordovaError) {
      log.error('Both LocalNotifications and cordova notification failed:', cordovaError)
    }
  }
}

function createNotifyTitle() {
  return i18n.global.t('form.dumping') as string
}

//请求notify权限
export async function queryNotificationPerm() {
  if (Platform.is.cordova) {
    try {
      // 使用 Capacitor LocalNotifications 请求权限
      const { LocalNotifications } = await import('@capacitor/local-notifications')
      const permission = await LocalNotifications.requestPermissions()

      if (permission.display === 'granted') {
        StrPubSub.subscribe(BysMarkerDumpingAlarm, async (data: BysMarkerAndUpdateInfo) => {
          let title = createNotifyTitle()
          if (checkIs4GMarker(data.MarkerType)) {
            const markerInfo = data.markerInfo ?? {}
            if (checkReportingIsPowerOn(markerInfo, true)) return

            const { AlarmReporting } = markerInfo
            title = reportLabels[AlarmReporting?.type ?? 3]?.()
          }

          //报警推送
          await scheduleLocalNotify({
            id: data.MarkerHWID as number,
            title,
            text: data.MarkerNo as string + ' ' + toLocalTime(data.markerInfo?.MarkerCmdTime),
          })

          //震动 - 使用 Capacitor Haptics
          try {
            const { Haptics, ImpactStyle } = await import('@capacitor/haptics')
            await Haptics.impact({ style: ImpactStyle.Heavy })
            setTimeout(async () => {
              await Haptics.impact({ style: ImpactStyle.Medium })
            }, 300)
            setTimeout(async () => {
              await Haptics.impact({ style: ImpactStyle.Heavy })
            }, 1100)
          } catch (error) {
            log.warn('Haptics failed, falling back to navigator.vibrate:', error)
            // 降级到原生振动
            if (navigator.vibrate) navigator.vibrate([1300, 1100])
          }
        })
      } else {
        log.warn('Notification permission not granted')
      }
    } catch (error) {
      log.warn('Failed to request notification permission with Capacitor, falling back to cordova:', error)
      // 降级到 Cordova 插件
      cordova.plugins.notification.local.requestPermission(function () {
        StrPubSub.subscribe(BysMarkerDumpingAlarm, (data: BysMarkerAndUpdateInfo) => {
          let title = createNotifyTitle()
          if (checkIs4GMarker(data.MarkerType)) {
            const markerInfo = data.markerInfo ?? {}
            if (checkReportingIsPowerOn(markerInfo, true)) return

            const { AlarmReporting } = markerInfo
            title = reportLabels[AlarmReporting?.type ?? 3]?.()
          }
          //报警推送 - 直接使用 Cordova 插件避免递归
          cordova.plugins.notification.local.schedule({
            id: data.MarkerHWID as number,
            title,
            text: data.MarkerNo as string + ' ' + toLocalTime(data.markerInfo?.MarkerCmdTime),
          })
          //震动
          navigator.vibrate([1300, 1100])
        })
      })
    }
  }
}

export function getBatteryPowerLabel(batteryPower: string): string {
  return parseInt(batteryPower as string, 16) === 0
    ? i18n.global.t('CmdTest.lowerPower') as string
    : calcMarkerPower(batteryPower as string)
}

const reportLabels = {
  1: () => i18n.global.t('CmdTest.reportOnStartup'),
  2: () => i18n.global.t('CmdTest.reportOnDebugging'),
  3: (actionCode) => actionCode === CmdCode.InfoReport ? i18n.global.t('CmdTest.reportOnRegular') : i18n.global.t('CmdTest.reportToAlarm'),
}

// 界桩设备状态
function getMarkerInfoReportState(report: bysproto.IBInfoReporting): string[] {
  const deviceState = decode4gMarkerInfoReportState(report.state ?? 0)
  const stateInfo: string[] = []
  const detailInfo_21: string[] = []
  if (deviceState.alarmLock) {
    stateInfo.push(i18n.global.t('CmdTest.alarmLock') as string)
  }
  if (deviceState.rtcClock) {
    stateInfo.push(i18n.global.t('CmdTest.rtcClockStatus', { status: i18n.global.t('CmdTest.abnormal') }) as string)
  }
  if (deviceState.gpsModule) {
    stateInfo.push(i18n.global.t('CmdTest.gpsModuleStatus', { status: i18n.global.t('CmdTest.abnormal') }) as string)
  }
  if (deviceState.axis3Sensor) {
    stateInfo.push(i18n.global.t('CmdTest.threeAxisSensorStatus', { status: i18n.global.t('CmdTest.abnormal') }) as string)
  }
  if (deviceState.battery) {
    stateInfo.push(i18n.global.t('CmdTest.batteryStatus', { status: i18n.global.t('CmdTest.abnormal') }) as string)
  }
  detailInfo_21.push(`${i18n.global.t('CmdTest.deviceState')}：${stateInfo.length > 0 ? stateInfo.join() : i18n.global.t('CmdTest.normal')}`)
  const InfoReport: string[] = [
    `${i18n.global.t('CmdTest.batteryVoltage')}: ${(report.battery ?? 0) / 1000}V`,
    `${i18n.global.t('CmdTest.deviceFieldStrength')}: ${report.signal ?? 0}dbm`,
    `${i18n.global.t('CmdTest.temperature')}: ${report.temp?.toFixed(1) ?? 0}°C`,
    `${i18n.global.t('CmdTest.humidity')}: ${report.rh?.toFixed(1) ?? 0}%`,
  ]
  detailInfo_21.push(...InfoReport)
  return detailInfo_21
}

// actionCode 22 报警原因和详细信息
function getAlarmStateDetailInfo(alarmstate: number, vibration, attitude: number): string[] {
  const detailInfo_22: string[] = []
  // 报警原因
  const alarmStateLabels: string[] = []
  const alarmState = decode4gMarkerAlarmState(alarmstate)
  if (alarmState.displacementAlarm) {
    alarmStateLabels.push(i18n.global.t('CmdTest.displacementAlarm') as string)
  }
  if (alarmState.vibrationAlarm) {
    alarmStateLabels.push(i18n.global.t('CmdTest.vibrationAlarm') as string)
  }
  if (alarmState.tiltAlarm) {
    alarmStateLabels.push(i18n.global.t('CmdTest.tiltAlarm') as string)
  }
  if (alarmState.infraredAlarm) {
    alarmStateLabels.push(i18n.global.t('CmdTest.infraredAlarm') as string)
  }
  detailInfo_22.push(`${i18n.global.t('form.alarmReason')}：${alarmStateLabels.length > 0 ? alarmStateLabels.join() : i18n.global.t('CmdTest.emptyReason')}`)
  // 振动报警
  if (Object.keys(vibration).length > 0) {
    detailInfo_22.push(i18n.global.t('CmdTest.vibrationAmplitudeVal', { value: vibration.amplitude }) as string)
    detailInfo_22.push(i18n.global.t('CmdTest.vibrationDurationVal', { value: vibration.duration }) as string)
  }
  // 倾斜报警
  if (alarmState.tiltAlarm) {
    detailInfo_22.push(`${i18n.global.t('form.attitude')}: ${attitude ?? 0}°`)
  }
  return detailInfo_22
}

export function getReporting(actionCode: number, reportInfo: string) {
  try {
    const report = JSON.parse(reportInfo)
    let reportInfoList = [`${i18n.global.t('CmdTest.reportType')}: ${reportLabels[report.type]?.(actionCode) ?? ''}`,]

    if (actionCode === 21) { // 信息上报 打卡
      const detailInfo_21 = getMarkerInfoReportState(report)
      reportInfoList.push(...detailInfo_21)
    } else if (actionCode === 22) { // 报警
      const detailInfo_22 = getAlarmStateDetailInfo(report.alarmstate, report.vibration ?? [], report.attitude ?? 0)
      reportInfoList.push(...detailInfo_22)
    }
    return reportInfoList
  } catch (e) {
    log.info('json parse report info error:', e)
  }
}

export function analysisMarkerStatus(row) {
  const reportInfo = row.ReportInfo
  let ret: string[] = []
  switch (row.ActionCode) {
    // 报警
    case CmdCode.D2:
      ret.push(i18n.global.t('form.dumping') as string)
      break
    case CmdCode.D3:
      ret.push(i18n.global.t('CmdTest.init') as string)
      break
    case CmdCode.D1:
      ret.push(`${i18n.global.t('CmdTest.report')}(${i18n.global.t('CmdTest.reportOnStartup')})`)
      break
    case CmdCode.InfoReport:
      row.detailInfo = getReporting(row.ActionCode, reportInfo)
      try {
        const report = JSON.parse(reportInfo)
        ret.push(`${i18n.global.t('CmdTest.report')}(${reportLabels[report.type]?.(row.ActionCode) ?? ''})`)
      } catch (e) {
        log.info('json parse err:', e)
      }
      break
    case CmdCode.AlarmReport:
      row.detailInfo = getReporting(row.ActionCode, reportInfo)
      try {
        const report = JSON.parse(reportInfo)
        ret.push(`${i18n.global.t('CmdTest.alarm')}(${reportLabels[report.type]?.(row.ActionCode) ?? ''})`)
      } catch (e) {
        log.info('json parse err:', e)
      }

      break
    default:
      ret.push(i18n.global.t('form.reportNormal') as string)
  }

  return ret.join(',')
}

export function analysis4gMarkerStatus(row: BysMarkerAndUpdateInfo) {
  log.info('analysis4gMarkerStatus:', row)
  const { InfoReporting, AlarmReporting } = row.markerInfo ?? {}
  const { alarmstate } = InfoReporting ?? AlarmReporting ?? {}
  // 不存在，或者正常参数0值，也不返回异常文本
  if (!alarmstate) {
    return ''
  }

  const alarmState = decode4gMarkerAlarmState(alarmstate)
  const alarmStateLabels: string[] = []
  if (alarmState.displacementAlarm) {
    alarmStateLabels.push(i18n.global.t('CmdTest.displacementAlarm') as string)
  }
  if (alarmState.vibrationAlarm) {
    alarmStateLabels.push(i18n.global.t('CmdTest.vibrationAlarm') as string)
  }
  if (alarmState.tiltAlarm) {
    alarmStateLabels.push(i18n.global.t('CmdTest.tiltAlarm') as string)
  }
  return i18n.global.t('CmdTest.threeAxisSensorStatus', { status: alarmStateLabels.join(', ') }) as string
}

export function getUserByRID(RID: string) {
  return User.getData(RID)
}

export function getOrgByRID(RID: string) {
  if (RID === DefUuid) {
    return i18n.global.t('builtInAttr.root')
  }
  return Unit.getData(RID)
}

export function getRoleByRID(RID: string) {
  return Role.getData(RID)
}

//获取对象原型链所有属性
export function getObjAllAttrs(obj: object): string[] {
  const Attrs: string[] = []
  for (let Attr in obj) {
    Attrs.push(Attr)
  }
  return Attrs
}

// eslint-disable-next-line @typescript-eslint/no-empty-function
export function emptyFn() {
}

export interface IControllerOptions {
  label: string,
  value: string,
  ControllerHWID?: number,
  MarkerHWID?: number,
  ControllerRID?: string,
  OrgRID: string,
  orderKey: number,
}

export function rawObjectProps(source: { [key: string]: any }): { [key: string]: any } {
  // 使用for..in..可以遍历到原型链上的属性
  const retObj: { [key: string]: any } = {}
  for (const k in source) {
    // 如果是原型链上的属性，则拷贝到当前对象中
    if (!Object.prototype.hasOwnProperty.call(source, k)) {
      let val = source[k]
      // val可能为null/undefined，不能调用Array.prototype.toString.call
      if (val !== null && val !== undefined && Array.prototype.toString.call(val) === '[object Object]') {
        val = rawObjectProps(val)
      }
      retObj[k] = val
    } else {
      retObj[k] = source[k]
    }
  }

  return retObj
}

export function decodeFieldStrengthSignal(value: number, unit = 'dbm'): string {
  const list = new Int16Array([value])
  const dv = new DataView(list.buffer)
  const val = dv.getInt16(0)
  return `${val} ${unit}`
}

// 检查上报的指令是否为开机上报，信息开机上报，报警开机上报
export function checkReportingIsPowerOn(markerInfo: IMarkerInfo, isAlarm = true): boolean {
  const { MarkerUploadCmd, AlarmReporting, InfoReporting } = markerInfo
  if (isAlarm) {
    return MarkerUploadCmd === CmdCode.AlarmReport && AlarmReporting?.type === 1
  }

  return MarkerUploadCmd === CmdCode.InfoReport && InfoReporting?.type === 1
}

// 判断界桩是否为报警状态
export function checkBysMarkerIsAlarm(data: BysMarkerAndUpdateInfo): boolean {
  if (checkIs4GMarker(data.MarkerType)) {
    if (!data.markerInfo) return false
    const { MarkerUploadCmd, AlarmReporting, AlarmStatusWith4G } = data.markerInfo as IMarkerInfo
    return MarkerUploadCmd === CmdCode.AlarmReport && AlarmReporting?.type !== 1 && AlarmStatusWith4G === 1
  }
  const { updateInfo } = data
  const { Cmd, StatusInfo } = updateInfo ?? {}
  return Cmd === CmdCode.D2 || (Cmd === CmdCode.D1 && (StatusInfo?.isAlarm ?? false))
}

export interface Deferred<T> extends Promise<T> {
  /**
   * Resolves the Deferred to a value T
   * @param value
   */
  resolve: (value?: T | PromiseLike<T>) => void
  //@ts-ignore: tsc guard
  /**
   * Rejects the Deferred
   * @param reason
   */
  reject: (reason?: any) => void
}

/**
 * Returns a Promise that has a resolve/reject methods that can
 * be used to resolve and defer the Deferred.
 */
export function deferred<T>(): Deferred<T> {
  let methods = {}
  const p = new Promise<T>((resolve, reject): void => {
    methods = { resolve, reject }
  })
  return Object.assign(p, methods) as Deferred<T>
}

export function waitDialogResult(opts: QDialogOptions): Promise<boolean> {
  return new Promise((resolve, reject) => {
    Dialog.create(opts)
      .onOk(() => {
        resolve(true)
      })
      .onCancel(() => {
        reject(false)
      })
      .onDismiss(() => {
        reject(false)
      })
  })
}

export function secondConfirmDialog(msg: string, okTitle: string = i18n.global.t('common.confirm'), cancelTitle: string = i18n.global.t('common.cancel'), opts?: Record<string, any>,): Promise<boolean> {
  const options = {
    message: msg,
    class: ['second-confirm'],
    ok: {
      label: okTitle,
    },
    cancel: {
      label: cancelTitle,
    },
    focus: 'cancel' as const,
    ...opts
  }

  return waitDialogResult(options)
    .then((res: boolean) => res)
    .catch((e: boolean) => e)
}

export function checkICCIDIsExpire(marker: bysdb.IDbBysMarker) {
  const beforeDay10 = getSubtractTime(utcTime(), -10, 'day')
  return !!marker.ICCID && dayjs(beforeDay10).isSameOrAfter(marker.ExpirationDate)
}

export enum nodeState {
  // bysMarker
  rmAlarm = 'marker-blue',
  warning = 'warning',
  removeWarning = 'removeWarning',
  abnormal = 'abnormal',
  unInstallStone = 'uninstall-stone',
  unInstallDevice = 'uninstall',

  // controller
  online = 'online',
  offline = 'offline',
  offlineError = 'offlineError',
  onlineError = 'onlineError',
}

// 界桩在数据状态变更或指令操作后，同步树形节点和地图图层对应的状态
// 调用该方法的场景：客户端下发的指令操作，界桩上报的指令(打卡、报警)，4g卡到期检测，客户端数据同步
export function updateMarkerStatusAfterAction(dbMarker: BysMarkerAndUpdateInfo): void {
  // 检测界桩所有可能存在的异常，如：未按时上报、低电量警报、遥晕/遥毙、4g卡到期
  let state = nodeState.rmAlarm
  if (!dbMarker.HasInstallStone) {
    state = nodeState.unInstallStone
  } else if (!dbMarker.HasInstallDevice) {
    state = nodeState.unInstallDevice
  } else if (checkoutAbnormalBysMarker(dbMarker, utcTime())) {
    state = nodeState.abnormal
  }
  // 检测到一个异常后，则直接更新对应组件的数据状态
  StrPubSub.publish(MarkerHasSomeAbnormal, dbMarker, state, {})
}

/***
 * 用于判断界桩是否存在异常，打卡为默认>=3天打卡即为异常
 * @param dbMarker 需要比对的界庄数据
 * @param nowDate 当前utc时间
 * @param abnormalTime 判断异常的时间差，默认3day
 */
export function checkoutAbnormalBysMarker(dbMarker: BysMarkerAndUpdateInfo, nowDate, abnormalTime = 3
                                          /*abnormalTime = webConfig.abnormalTime ?? 3*/): boolean {

  // 4g界桩
  if (checkIs4GMarker(dbMarker.MarkerType)) {
    return checkoutAbnormalBysMarker4g(dbMarker, nowDate, abnormalTime)
  }

  // 常规界桩
  const markerInfo = dbMarker.markerInfo
  const haveMaintainPerm = isHaveMaintainPerm()
  if (!markerInfo) { // 不存在makrerInfo判定为未按时上报，未按时上报需维修权限才可看到
    return haveMaintainPerm
  }

  // 低电量报警只有维修权限的用户才能看到
  if (haveMaintainPerm && decodeDeviceStatus(markerInfo.Status as number).isLowPowerAlarm) {
    return true
  }
  if (!markerInfo.MarkerCmdTime) {
    return true
  }
  return getDeffDay(markerInfo.MarkerCmdTime as string, nowDate) >= abnormalTime
}

function checkoutAbnormalBysMarker4g(dbMarker: BysMarkerAndUpdateInfo, nowDate, abnormalTime = 3): boolean {
  // 判断4g界桩4g卡到期异常
  // 判断异常是只判断已安装设备的界桩 但是4g卡到期异常的界桩可以存在未安装设备
  const isExpire = checkICCIDIsExpire(dbMarker)
  if (isExpire) return true
  const haveMaintainPerm = isHaveMaintainPerm()
  // 判断4g界桩的遥晕遥毙异常
  const isMarkerDisabled = dbMarker?.MarkerDisabled !== 0
  if (isMarkerDisabled) return haveMaintainPerm

  // 存在维修权限才能看到"未按时上报"的异常
  if (haveMaintainPerm) {
    // 时间异常
    const markerInfo = dbMarker.markerInfo
    if (!markerInfo?.MarkerCmdTime) {
      return true
    }
    const timeAbnormal = getDeffDay(markerInfo.MarkerCmdTime as string, nowDate) >= abnormalTime
    if (timeAbnormal) {
      return true
    }

    // 判断4g界桩低电量异常
    if ((markerInfo.InfoReporting?.battery as number) <= MarkerLowPower4g) return true
  }
  // 当前是正常打卡，还是报警
  // 界桩异常统计中没有统计报警界桩
  // let isAlarmReport = marker.MarkerUploadCmd === CmdCode.AlarmReport && marker.AlarmReporting?.type === 1 && marker.AlarmStatusWith4G === 0
  // if (isAlarmReport) {
  //   return true
  // }

  // 打卡上报
  // 界桩异常统计中没有统计打卡上报的异常
  // if (!marker.InfoReporting || marker.InfoReporting.state !== 0) return true

  return false
}

export function partialUpdateMarker(data: bysdb.IDbBysMarker, param: crud.IDMLParam, opts?: ICallOption, beforeAction?: Function) {
  return new Promise((resolve, reject) => {
    beforeAction?.()
    const rpcMeta = new TrpcMeta()
    rpcMeta.SetProtoMsg('DMLParam', new crud.DMLParam(param))
    const options: ICallOption = {
      OnResult(res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) {
        log.info('IDbBysMarker Update Setting result', res, rpcCmd, meta)
        resolve(true)
      },
      OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
        log.error('IDbBysMarker Update Setting server error', errRpc)
        reject(false)
      },
      OnLocalErr: (err: any) => {
        log.error('IDbBysMarker Update Setting local error', err)
        reject(false)
      },
      OnTimeout: (v: any) => {
        log.warn('IDbBysMarker Update Setting timeout', v)
        reject(false)
      },
      ...opts,
      rpcMeta,
    }
    PrpcDbBysMarker.PartialUpdate(data, options)
  })
}

export function partialUpdateMarkerSetting(data: bysdb.IDbBysMarker, param: crud.IDMLParam, settingOption: Record<string, string>) {
  try {
    const marker = Object.assign({}, data)
    const setting = JSON.parse(marker.Setting ?? '{}')
    Object.assign(setting, settingOption)
    const settingStr = JSON.stringify(setting)
    marker.Setting = settingStr
    const rpcMeta = new TrpcMeta()
    rpcMeta.SetProtoMsg('DMLParam', new crud.DMLParam(param))
    const options: ICallOption = {
      OnResult(res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) {
        log.info('IDbBysMarker Update Setting result', res, rpcCmd, meta)
        if (res.AffectedRow === 0) {
          Notify.create({
            message: i18n.global.t('menus.syncSettingTime') as string,
            color: 'red',
            icon: 'error',
            position: 'top',
          })
          return
        }
        BysMarker.setPartData(marker.RID as string, { Setting: settingStr })
        // 从DeviceTree遥晕遥毙或异常界桩解除遥晕时需要将MarkerDisabled和CameraDisabled同步到界桩编辑表单
        StrPubSub.publish(UpdateMarkerForm, marker.RID, ['Setting', 'MarkerDisabled', 'CameraDisabled'])
      },
      rpcMeta,
    }
    partialUpdateMarker(marker, param, options)
  } catch (e) {
    log.info('JSON parse error:', e)
  }
}

export function objUnderline2SmallHump<T>(obj: Record<string, any>, type: (new (obj: any) => T) = Object as any): T {
  for (const key in obj) {
    if (key.includes('_')) {
      const newKey = key.replace(/(?<!^)_([a-zA-Z])/g, (match, key1) => key1.toUpperCase())
      obj[newKey] = obj[key]
      delete obj[key]
    }
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      obj[key] = objUnderline2SmallHump(obj[key], type)
    }
  }
  return new type(obj)
}
