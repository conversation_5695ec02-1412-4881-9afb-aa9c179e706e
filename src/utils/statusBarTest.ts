/**
 * StatusBar 模块测试工具
 * 用于诊断 StatusBar 模块导入问题
 */

import { Platform } from 'quasar'
import { isApp } from '../config'
import log from './log'

export async function testStatusBarImport() {
  console.log('=== StatusBar Import Test ===')
  console.log('Platform.is.capacitor:', Platform.is.capacitor)
  console.log('Platform.is.cordova:', Platform.is.cordova)
  console.log('Platform.is.android:', Platform.is.android)
  console.log('Platform.is.ios:', Platform.is.ios)
  console.log('Platform.is.mobile:', Platform.is.mobile)
  console.log('Platform.is.desktop:', Platform.is.desktop)
  console.log('Platform.is.electron:', Platform.is.electron)

  // 检查全局 Capacitor 对象
  if (typeof window !== 'undefined') {
    console.log('Window object available')
    console.log('window.Capacitor available:', !!(window as any).Capacitor)

    if ((window as any).Capacitor) {
      const capacitor = (window as any).Capacitor
      console.log('Capacitor object keys:', Object.keys(capacitor))
      console.log('Capacitor.Plugins available:', !!capacitor.Plugins)

      if (capacitor.Plugins) {
        console.log('Available plugins:', Object.keys(capacitor.Plugins))
        console.log('StatusBar plugin available:', !!capacitor.Plugins.StatusBar)

        if (capacitor.Plugins.StatusBar) {
          console.log('StatusBar plugin methods:', Object.getOwnPropertyNames(capacitor.Plugins.StatusBar))
        }
      }
    }
  }

  if (!isApp) {
    console.log('Not in App environment, skipping direct import test')
    return false
  }

  try {
    console.log('Attempting direct import of @capacitor/status-bar...')
    const statusBarModule = await import(/* @vite-ignore */'@capacitor/status-bar')
    console.log('Direct import successful!')
    console.log('Module keys:', Object.keys(statusBarModule))
    console.log('StatusBar available:', !!statusBarModule.StatusBar)
    console.log('Style available:', !!statusBarModule.Style)

    if (statusBarModule.StatusBar) {
      console.log('StatusBar methods:', Object.getOwnPropertyNames(statusBarModule.StatusBar))
    }

    if (statusBarModule.Style) {
      console.log('Style values:', statusBarModule.Style)
    }

    return true
  } catch (error) {
    console.error('Direct import failed:', error)

    // 尝试从全局 Capacitor 对象获取
    if (typeof window !== 'undefined' && (window as any).Capacitor?.Plugins?.StatusBar) {
      console.log('Found StatusBar in global Capacitor.Plugins, this should work with our new approach')
      return true
    }

    return false
  }
}

// 在开发模式下自动运行测试
if (process.env.NODE_ENV === 'development') {
  // 延迟执行，确保 Capacitor 环境已经初始化
  setTimeout(() => {
    testStatusBarImport().then(success => {
      console.log('StatusBar test completed, success:', success)
    })
  }, 1000)
}
