/**
 * Capacitor 工具函数
 * 用于安全地导入和使用 Capacitor 插件，避免在 Web 端编译错误
 */

import { Platform } from 'quasar'
import log from './log'

/**
 * 安全地导入 Capacitor 模块
 * @param moduleName 模块名称
 * @returns 模块对象或 null
 */
export async function safeImportCapacitor<T = any>(moduleName: string): Promise<T | null> {
  // 只在 Capacitor 环境下尝试导入
  if (!Platform.is.capacitor) {
    log.info(`Not in Capacitor environment, skipping import of ${moduleName}`)
    return null
  }

  try {
    log.info(`Attempting to import Capacitor module: ${moduleName}`)

    // 尝试从全局 Capacitor 对象获取插件
    if (typeof window !== 'undefined' && (window as any).Capacitor) {
      const capacitor = (window as any).Capacitor
      log.info('Capacitor object found on window')

      // 根据模块名获取对应的插件
      const pluginName = getPluginNameFromModule(moduleName)
      if (pluginName && capacitor.Plugins && capacitor.Plugins[pluginName]) {
        log.info(`Found plugin ${pluginName} in Capacitor.Plugins`)
        return createModuleFromPlugin(capacitor.Plugins[pluginName], moduleName)
      }
    }

    // 降级到动态导入
    const module = await import(/* @vite-ignore */moduleName)
    log.info(`Successfully imported Capacitor module: ${moduleName}`)
    return module
  } catch (error) {
    log.error(`Failed to import Capacitor module ${moduleName}:`, error)
    return null
  }
}

// 从模块名获取插件名
function getPluginNameFromModule(moduleName: string): string | null {
  const moduleMap: Record<string, string> = {
    '@capacitor/status-bar': 'StatusBar',
    '@capacitor/geolocation': 'Geolocation',
    '@capacitor/local-notifications': 'LocalNotifications',
    '@capacitor/camera': 'Camera',
    '@capacitor/app-launcher': 'AppLauncher',
    '@capacitor-community/text-to-speech': 'TextToSpeech'
  }
  return moduleMap[moduleName] || null
}

// 从插件创建模块对象
function createModuleFromPlugin(plugin: any, moduleName: string): any {
  switch (moduleName) {
    case '@capacitor/status-bar':
      return {
        StatusBar: plugin,
        Style: plugin.Style || { Light: 'LIGHT', Dark: 'DARK', Default: 'DEFAULT' }
      }
    case '@capacitor/geolocation':
      return { Geolocation: plugin }
    case '@capacitor/local-notifications':
      return { LocalNotifications: plugin }
    case '@capacitor/camera':
      return {
        Camera: plugin,
        CameraResultType: plugin.CameraResultType || {},
        CameraSource: plugin.CameraSource || {}
      }
    case '@capacitor/app-launcher':
      return { AppLauncher: plugin }
    case '@capacitor-community/text-to-speech':
      return { TextToSpeech: plugin }
    default:
      return plugin
  }
}

/**
 * 安全地使用 LocalNotifications
 */
export async function safeLocalNotifications() {
  const module = await safeImportCapacitor('@capacitor/local-notifications')
  return module?.LocalNotifications || null
}

/**
 * 安全地使用 Geolocation
 */
export async function safeGeolocation() {
  const module = await safeImportCapacitor('@capacitor/geolocation')
  return module?.Geolocation || null
}

/**
 * 安全地使用 TextToSpeech
 */
export async function safeTextToSpeech() {
  const module = await safeImportCapacitor('@capacitor-community/text-to-speech')
  return module?.TextToSpeech || null
}

/**
 * 安全地使用 StatusBar
 */
export async function safeStatusBar() {
  return await safeImportCapacitor('@capacitor/status-bar')
}

/**
 * 安全地使用 Camera
 */
export async function safeCamera() {
  const module = await safeImportCapacitor('@capacitor/camera')
  return module || null
}

/**
 * 安全地使用 AppLauncher
 */
export async function safeAppLauncher() {
  const module = await safeImportCapacitor('@capacitor/app-launcher')
  return module?.AppLauncher || null
}