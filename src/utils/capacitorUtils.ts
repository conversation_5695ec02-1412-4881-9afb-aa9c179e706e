/**
 * Capacitor 工具函数
 * 用于安全地导入和使用 Capacitor 插件，避免在 Web 端编译错误
 */
import log from './log'

// 模块缓存
const moduleCache = new Map<string, any>()

// 模块名到插件名的映射
const moduleToPluginMap: Record<string, string> = {
  '@capacitor/status-bar': 'StatusBar',
  '@capacitor/geolocation': 'Geolocation',
  '@capacitor/local-notifications': 'LocalNotifications',
  '@capacitor/camera': 'Camera',
  '@capacitor/app-launcher': 'AppLauncher',
  '@capacitor-community/text-to-speech': 'TextToSpeech',
  '@capacitor/file-transfer': 'FileTransfer',
  '@capacitor/filesystem': 'Filesystem',
  '@capacitor/file-viewer': 'FileViewer',
  '@capacitor/haptics': 'Haptics'
}

/**
 * 安全地导入 Capacitor 模块
 * @param moduleName 模块名称
 * @returns 模块对象或 null
 */
export async function safeImportCapacitor<T = any>(moduleName: string): Promise<T | null> {
  // 检查缓存
  if (moduleCache.has(moduleName)) {
    return moduleCache.get(moduleName)
  }

  // 检查是否在 Capacitor 环境中
  if (typeof window !== 'undefined' && (window as any).Capacitor?.Plugins) {
    const pluginName = moduleToPluginMap[moduleName]
    if (pluginName) {
      const plugin = (window as any).Capacitor.Plugins[pluginName]
      if (plugin) {
        // 直接返回插件对象，不需要重新包装
        moduleCache.set(moduleName, plugin)
        log.info(`Successfully loaded Capacitor plugin: ${pluginName}`)
        return plugin
      } else {
        log.warn(`Capacitor plugin ${pluginName} not found in Capacitor.Plugins`)
        return null
      }
    } else {
      log.warn(`No plugin mapping found for module: ${moduleName}`)
      return null
    }
  }

  // 如果不在 Capacitor 环境中，返回 null（不尝试动态导入）
  log.warn(`Not in Capacitor environment, module ${moduleName} not available`)
  return null
}



/**
 * 安全地使用 LocalNotifications
 */
export async function safeLocalNotifications() {
  return await safeImportCapacitor('@capacitor/local-notifications')
}

/**
 * 安全地使用 Geolocation
 */
export async function safeGeolocation() {
  return await safeImportCapacitor('@capacitor/geolocation')
}

/**
 * 安全地使用 TextToSpeech
 */
export async function safeTextToSpeech() {
  return await safeImportCapacitor('@capacitor-community/text-to-speech')
}

/**
 * 安全地使用 StatusBar
 */
export async function safeStatusBar() {
  return await safeImportCapacitor('@capacitor/status-bar')
}

/**
 * 安全地使用 Camera
 */
export async function safeCamera() {
  return await safeImportCapacitor('@capacitor/camera')
}

/**
 * 安全地使用 AppLauncher
 */
export async function safeAppLauncher() {
  return await safeImportCapacitor('@capacitor/app-launcher')
}

/**
 * 安全地使用 FileTransfer
 */
export async function safeFileTransfer() {
  return await safeImportCapacitor('@capacitor/file-transfer')
}

/**
 * 安全地使用 Filesystem
 */
export async function safeFilesystem() {
  return await safeImportCapacitor('@capacitor/filesystem')
}

/**
 * 安全地使用 FileViewer
 */
export async function safeFileViewer() {
  return await safeImportCapacitor('@capacitor/file-viewer')
}

/**
 * 安全地使用 Haptics
 */
export async function safeHaptics() {
  return await safeImportCapacitor('@capacitor/haptics')
}