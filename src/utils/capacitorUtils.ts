/**
 * Capacitor 工具函数
 * 用于安全地导入和使用 Capacitor 插件，避免在 Web 端编译错误
 */
import log from './log'

// 模块缓存
const moduleCache = new Map<string, any>()

/**
 * 安全地导入 Capacitor 模块
 * @param moduleName 模块名称
 * @returns 模块对象或 null
 */
export async function safeImportCapacitor<T = any>(moduleName: string): Promise<T | null> {
  // 检查缓存
  if (moduleCache.has(moduleName)) {
    return moduleCache.get(moduleName)
  }

  try {
    // 动态导入模块
    const module = await import(/* @vite-ignore */moduleName)

    // 缓存模块
    moduleCache.set(moduleName, module)

    return module
  } catch (error) {
    log.error(`Failed to import Capacitor module ${moduleName}:`, error)

    // 缓存失败结果，避免重复尝试
    moduleCache.set(moduleName, null)

    return null
  }
}

/**
 * 安全地使用 LocalNotifications
 */
export async function safeLocalNotifications() {
  return await safeImportCapacitor('@capacitor/local-notifications')
}

/**
 * 安全地使用 Geolocation
 */
export async function safeGeolocation() {
  return await safeImportCapacitor('@capacitor/geolocation')
}

/**
 * 安全地使用 TextToSpeech
 */
export async function safeTextToSpeech() {
  return await safeImportCapacitor('@capacitor-community/text-to-speech')
}

/**
 * 安全地使用 StatusBar
 */
export async function safeStatusBar() {
  return await safeImportCapacitor('@capacitor/status-bar')
}

/**
 * 安全地使用 Camera
 */
export async function safeCamera() {
  return await safeImportCapacitor('@capacitor/camera')
}

/**
 * 安全地使用 AppLauncher
 */
export async function safeAppLauncher() {
  return await safeImportCapacitor('@capacitor/app-launcher')
}

/**
 * 安全地使用 FileTransfer
 */
export async function safeFileTransfer() {
  return await safeImportCapacitor('@capacitor/file-transfer')
}

/**
 * 安全地使用 Filesystem
 */
export async function safeFilesystem() {
  return await safeImportCapacitor('@capacitor/filesystem')
}

/**
 * 安全地使用 FileViewer
 */
export async function safeFileViewer() {
  return await safeImportCapacitor('@capacitor/file-viewer')
}

/**
 * 安全地使用 Haptics
 */
export async function safeHaptics() {
  return await safeImportCapacitor('@capacitor/haptics')
}