/**
 * Capacitor 工具函数
 * 用于安全地导入和使用 Capacitor 插件，避免在 Web 端编译错误
 */
import log from './log'

// 模块缓存
const moduleCache = new Map<string, any>()

// 模块名到插件名的映射
const moduleToPluginMap: Record<string, string> = {
  '@capacitor/status-bar': 'StatusBar',
  '@capacitor/geolocation': 'Geolocation',
  '@capacitor/local-notifications': 'LocalNotifications',
  '@capacitor/camera': 'Camera',
  '@capacitor/app-launcher': 'AppLauncher',
  '@capacitor-community/text-to-speech': 'TextToSpeech',
  '@capacitor/file-transfer': 'FileTransfer',
  '@capacitor/filesystem': 'Filesystem',
  '@capacitor/file-viewer': 'FileViewer',
  '@capacitor/haptics': 'Haptics'
}

/**
 * 安全地导入 Capacitor 模块
 * @param moduleName 模块名称
 * @returns 模块对象或 null
 */
export async function safeImportCapacitor<T = any>(moduleName: string): Promise<T | null> {
  // 检查缓存
  if (moduleCache.has(moduleName)) {
    return moduleCache.get(moduleName)
  }

  try {
    // 优先从全局 Capacitor.Plugins 获取
    if (typeof window !== 'undefined' && (window as any).Capacitor?.Plugins) {
      const pluginName = moduleToPluginMap[moduleName]
      if (pluginName) {
        const plugin = (window as any).Capacitor.Plugins[pluginName]
        if (plugin) {
          const module = createModuleFromPlugin(plugin, moduleName)
          // 缓存模块
          moduleCache.set(moduleName, module)
          log.info(`Successfully loaded Capacitor plugin: ${pluginName}`)
          return module
        }
      }
    }

    // 降级到动态导入（主要用于 Web 端开发）
    try {
      const module = await import(/* @vite-ignore */moduleName)
      // 缓存模块
      moduleCache.set(moduleName, module)
      log.info(`Successfully imported Capacitor module: ${moduleName}`)
      return module
    } catch (importError) {
      log.warn(`Dynamic import failed for ${moduleName}, plugin may not be available in this environment`)
    }

    // 缓存失败结果，避免重复尝试
    moduleCache.set(moduleName, null)
    return null

  } catch (error) {
    log.error(`Failed to load Capacitor module ${moduleName}:`, error)

    // 缓存失败结果，避免重复尝试
    moduleCache.set(moduleName, null)

    return null
  }
}

// 从插件创建模块对象
function createModuleFromPlugin(plugin: any, moduleName: string): any {
  switch (moduleName) {
    case '@capacitor/status-bar':
      return {
        StatusBar: plugin,
        Style: plugin.Style || { Light: 'LIGHT', Dark: 'DARK', Default: 'DEFAULT' }
      }
    case '@capacitor/geolocation':
      return { Geolocation: plugin }
    case '@capacitor/local-notifications':
      return { LocalNotifications: plugin }
    case '@capacitor/camera':
      return {
        Camera: plugin,
        CameraResultType: plugin.CameraResultType || {},
        CameraSource: plugin.CameraSource || {}
      }
    case '@capacitor/app-launcher':
      return { AppLauncher: plugin }
    case '@capacitor-community/text-to-speech':
      return { TextToSpeech: plugin }
    case '@capacitor/file-transfer':
      return { FileTransfer: plugin }
    case '@capacitor/filesystem':
      return {
        Filesystem: plugin,
        Directory: plugin.Directory || {}
      }
    case '@capacitor/file-viewer':
      return { FileViewer: plugin }
    case '@capacitor/haptics':
      return { Haptics: plugin }
    default:
      return plugin
  }
}

/**
 * 安全地使用 LocalNotifications
 */
export async function safeLocalNotifications() {
  return await safeImportCapacitor('@capacitor/local-notifications')
}

/**
 * 安全地使用 Geolocation
 */
export async function safeGeolocation() {
  return await safeImportCapacitor('@capacitor/geolocation')
}

/**
 * 安全地使用 TextToSpeech
 */
export async function safeTextToSpeech() {
  return await safeImportCapacitor('@capacitor-community/text-to-speech')
}

/**
 * 安全地使用 StatusBar
 */
export async function safeStatusBar() {
  return await safeImportCapacitor('@capacitor/status-bar')
}

/**
 * 安全地使用 Camera
 */
export async function safeCamera() {
  return await safeImportCapacitor('@capacitor/camera')
}

/**
 * 安全地使用 AppLauncher
 */
export async function safeAppLauncher() {
  return await safeImportCapacitor('@capacitor/app-launcher')
}

/**
 * 安全地使用 FileTransfer
 */
export async function safeFileTransfer() {
  return await safeImportCapacitor('@capacitor/file-transfer')
}

/**
 * 安全地使用 Filesystem
 */
export async function safeFilesystem() {
  return await safeImportCapacitor('@capacitor/filesystem')
}

/**
 * 安全地使用 FileViewer
 */
export async function safeFileViewer() {
  return await safeImportCapacitor('@capacitor/file-viewer')
}

/**
 * 安全地使用 Haptics
 */
export async function safeHaptics() {
  return await safeImportCapacitor('@capacitor/haptics')
}