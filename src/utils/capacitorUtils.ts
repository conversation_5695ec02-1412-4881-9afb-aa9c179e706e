/**
 * Capacitor 工具函数
 * 用于安全地导入和使用 Capacitor 插件，避免在 Web 端编译错误
 */

import { Platform } from 'quasar'
import log from './log'

/**
 * 安全地导入 Capacitor 模块
 * @param moduleName 模块名称
 * @returns 模块对象或 null
 */
export async function safeImportCapacitor<T = any>(moduleName: string): Promise<T | null> {
  // 只在 Capacitor 环境下尝试导入
  if (!Platform.is.capacitor) {
    return null
  }

  try {
    const module = await import(/* @vite-ignore */moduleName)
    return module
  } catch (error) {
    log.warn(`Failed to import Capacitor module ${moduleName}:`, error)
    return null
  }
}

/**
 * 安全地使用 LocalNotifications
 */
export async function safeLocalNotifications() {
  const module = await safeImportCapacitor('@capacitor/local-notifications')
  return module?.LocalNotifications || null
}

/**
 * 安全地使用 Geolocation
 */
export async function safeGeolocation() {
  const module = await safeImportCapacitor('@capacitor/geolocation')
  return module?.Geolocation || null
}

/**
 * 安全地使用 TextToSpeech
 */
export async function safeTextToSpeech() {
  const module = await safeImportCapacitor('@capacitor-community/text-to-speech')
  return module?.TextToSpeech || null
}



/**
 * 安全地使用 StatusBar
 */
export async function safeStatusBar() {
  const module = await safeImportCapacitor('@capacitor/status-bar')
  return module ? { StatusBar: module.StatusBar, Style: module.Style } : null
}

/**
 * 安全地使用 Camera
 */
export async function safeCamera() {
  const module = await safeImportCapacitor('@capacitor/camera')
  return module ? { Camera: module.Camera, CameraResultType: module.CameraResultType, CameraSource: module.CameraSource } : null
}

/**
 * 安全地使用 AppLauncher
 */
export async function safeAppLauncher() {
  const module = await safeImportCapacitor('@capacitor/app-launcher')
  return module?.AppLauncher || null
}

/**
 * 检查是否在 Capacitor 环境
 */
export function isCapacitorEnvironment(): boolean {
  return Platform.is.capacitor || false
}

/**
 * 检查是否在 Web 环境
 */
export function isWebEnvironment(): boolean {
  return !Platform.is.capacitor && !Platform.is.cordova
}
