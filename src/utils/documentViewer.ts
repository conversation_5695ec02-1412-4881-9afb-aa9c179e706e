import log from '@utils/log'
import { resolveServerPath } from './path'
import { FileViewer } from '@capacitor/file-viewer'
import { Filesystem, Directory } from '@capacitor/filesystem'
import { FileTransfer } from '@capacitor/file-transfer'

export async function openPdf(url: string) {
  try {
    const fileName = url.split('/').pop()
    if (!fileName) {
      log.error('openPdf invalid file path', fileName, url)
      return
    }

    const fileInfo = await Filesystem.getUri({
      directory: Directory.Cache,
      path: fileName
    })

    // 1. 检查本地缓存是否存在
    let needDownload = false
    try {
      const stat = await Filesystem.stat({
        directory: Directory.Cache,
        path: fileName,
      })
      // 判断文件是否超过7天（单位：毫秒）
      const now = Date.now()
      const mtime = typeof stat.mtime === 'number' ? stat.mtime : new Date(stat.mtime).getTime()
      const oneWeek = 7 * 24 * 60 * 60 * 1000
      if (now - mtime > oneWeek) {
        needDownload = true
      }
    } catch {
      // 文件不存在
      needDownload = true
    }

    // 2. 如果文件不存在，则先下载
    if (needDownload) {
      const assetUrl = resolveServerPath('/' + url)
      await FileTransfer.downloadFile({
        path: fileInfo.uri,
        url: assetUrl,
        progress: false,
        recursive: true,
      })
    }

    // 打开文档
    await FileViewer.openDocumentFromLocalPath({
      path: fileInfo.uri,
    })
  } catch (err) {
    log.error('openPdf error', JSON.stringify(err))
  }
}
