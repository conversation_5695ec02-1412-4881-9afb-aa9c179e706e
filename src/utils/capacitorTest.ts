/**
 * Capacitor 插件测试工具
 * 用于诊断和测试 Capacitor 插件的可用性
 */

/**
 * 测试 Capacitor 环境和插件可用性
 */
export function testCapacitorEnvironment(): void {
  console.log('=== Capacitor Environment Test ===')
  
  // 检查全局对象
  console.log('window object available:', typeof window !== 'undefined')
  
  if (typeof window === 'undefined') {
    console.log('Not in browser environment')
    return
  }
  
  // 检查 Capacitor 对象
  const capacitor = (window as any).Capacitor
  console.log('window.Capacitor available:', !!capacitor)
  
  if (!capacitor) {
    console.log('Capacitor not available - likely running in web mode')
    return
  }
  
  console.log('Capacitor object keys:', Object.keys(capacitor))
  console.log('Capacitor.Plugins available:', !!capacitor.Plugins)
  
  if (!capacitor.Plugins) {
    console.log('Capacitor.Plugins not available')
    return
  }
  
  // 列出所有可用的插件
  const availablePlugins = Object.keys(capacitor.Plugins)
  console.log('Available plugins:', availablePlugins)
  
  // 测试特定插件
  const pluginsToTest = [
    'StatusBar',
    'Geolocation', 
    'LocalNotifications',
    'Camera',
    'AppLauncher',
    'TextToSpeech',
    'FileTransfer',
    'Filesystem',
    'FileViewer',
    'Haptics'
  ]
  
  console.log('\n=== Plugin Availability Test ===')
  pluginsToTest.forEach(pluginName => {
    const plugin = capacitor.Plugins[pluginName]
    const available = !!plugin
    console.log(`${pluginName}: ${available ? '✅ Available' : '❌ Not Available'}`)
    
    if (available && plugin) {
      // 显示插件的方法
      try {
        const methods = Object.getOwnPropertyNames(plugin).filter(name => 
          typeof plugin[name] === 'function' && !name.startsWith('_')
        )
        if (methods.length > 0) {
          console.log(`  Methods: ${methods.slice(0, 5).join(', ')}${methods.length > 5 ? '...' : ''}`)
        }
      } catch (error) {
        console.log(`  Error getting methods: ${error}`)
      }
    }
  })
  
  console.log('\n=== Test Complete ===')
}

/**
 * 测试特定插件的功能
 */
export async function testSpecificPlugin(pluginName: string): Promise<boolean> {
  console.log(`\n=== Testing ${pluginName} Plugin ===`)
  
  if (typeof window === 'undefined' || !(window as any).Capacitor?.Plugins) {
    console.log('Capacitor environment not available')
    return false
  }
  
  const plugin = (window as any).Capacitor.Plugins[pluginName]
  if (!plugin) {
    console.log(`${pluginName} plugin not available`)
    return false
  }
  
  console.log(`${pluginName} plugin found`)
  
  // 根据插件类型进行简单测试
  try {
    switch (pluginName) {
      case 'StatusBar':
        console.log('StatusBar plugin ready for use')
        break
        
      case 'Haptics':
        console.log('Haptics plugin ready for use')
        break
        
      case 'Filesystem':
        if (plugin.Directory) {
          console.log('Filesystem directories available:', Object.keys(plugin.Directory))
        }
        console.log('Filesystem plugin ready for use')
        break
        
      default:
        console.log(`${pluginName} plugin available`)
    }
    
    return true
  } catch (error) {
    console.error(`Error testing ${pluginName}:`, error)
    return false
  }
}
