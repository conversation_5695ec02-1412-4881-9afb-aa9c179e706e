/**
 * Capacitor 调试工具
 * 用于诊断 Capacitor 环境和插件状态
 */

/**
 * 检查 Capacitor 环境状态
 */
export function debugCapacitorEnvironment(): void {
  console.log('=== Capacitor Environment Debug ===')
  
  // 检查基本环境
  console.log('typeof window:', typeof window)
  console.log('window !== undefined:', typeof window !== 'undefined')
  
  if (typeof window === 'undefined') {
    console.log('❌ Window object not available - not in browser environment')
    return
  }
  
  // 检查 Capacitor 对象
  const capacitor = (window as any).Capacitor
  console.log('window.Capacitor:', capacitor)
  console.log('typeof window.Capacitor:', typeof capacitor)
  console.log('window.Capacitor exists:', !!capacitor)
  
  if (!capacitor) {
    console.log('❌ Capacitor object not found')
    console.log('Available window properties:', Object.getOwnPropertyNames(window).filter(name => name.toLowerCase().includes('capacitor')))
    return
  }
  
  // 检查 Capacitor 对象的属性
  console.log('Capacitor object type:', typeof capacitor)
  console.log('Capacitor object keys:', Object.keys(capacitor))
  console.log('Capacitor object properties:', Object.getOwnPropertyNames(capacitor))
  
  // 检查 Plugins 对象
  console.log('Capacitor.Plugins:', capacitor.Plugins)
  console.log('typeof Capacitor.Plugins:', typeof capacitor.Plugins)
  console.log('Capacitor.Plugins exists:', !!capacitor.Plugins)
  
  if (!capacitor.Plugins) {
    console.log('❌ Capacitor.Plugins not found')
    return
  }
  
  // 列出所有可用的插件
  const availablePlugins = Object.keys(capacitor.Plugins)
  console.log('✅ Available plugins count:', availablePlugins.length)
  console.log('✅ Available plugins:', availablePlugins)
  
  // 检查特定插件
  const pluginsToCheck = ['StatusBar', 'Filesystem', 'Haptics', 'Geolocation', 'LocalNotifications']
  
  console.log('\n=== Plugin Details ===')
  pluginsToCheck.forEach(pluginName => {
    const plugin = capacitor.Plugins[pluginName]
    console.log(`\n${pluginName}:`)
    console.log(`  Available: ${!!plugin}`)
    
    if (plugin) {
      console.log(`  Type: ${typeof plugin}`)
      console.log(`  Constructor: ${plugin.constructor?.name}`)
      
      const methods = Object.getOwnPropertyNames(plugin).filter(name => 
        typeof plugin[name] === 'function' && !name.startsWith('_')
      )
      console.log(`  Methods (${methods.length}):`, methods.slice(0, 10)) // 只显示前10个方法
      
      const properties = Object.getOwnPropertyNames(plugin).filter(name => 
        typeof plugin[name] !== 'function' && !name.startsWith('_')
      )
      console.log(`  Properties (${properties.length}):`, properties.slice(0, 5)) // 只显示前5个属性
    }
  })
  
  console.log('\n=== Environment Summary ===')
  console.log(`✅ Window: Available`)
  console.log(`${capacitor ? '✅' : '❌'} Capacitor: ${capacitor ? 'Available' : 'Not Available'}`)
  console.log(`${capacitor?.Plugins ? '✅' : '❌'} Plugins: ${capacitor?.Plugins ? 'Available' : 'Not Available'}`)
  console.log(`📊 Plugin Count: ${availablePlugins.length}`)
}

/**
 * 测试特定插件的基本功能
 */
export function testPlugin(pluginName: string): boolean {
  console.log(`\n=== Testing Plugin: ${pluginName} ===`)
  
  if (typeof window === 'undefined' || !(window as any).Capacitor?.Plugins) {
    console.log('❌ Capacitor environment not available')
    return false
  }
  
  const plugin = (window as any).Capacitor.Plugins[pluginName]
  if (!plugin) {
    console.log(`❌ Plugin ${pluginName} not found`)
    return false
  }
  
  console.log(`✅ Plugin ${pluginName} found`)
  console.log(`Plugin type:`, typeof plugin)
  console.log(`Plugin constructor:`, plugin.constructor?.name)
  
  // 尝试调用一些基本方法（如果存在）
  try {
    if (pluginName === 'Filesystem' && plugin.Directory) {
      console.log('Filesystem.Directory:', plugin.Directory)
      console.log('Directory keys:', Object.keys(plugin.Directory))
    }
    
    if (pluginName === 'StatusBar' && plugin.Style) {
      console.log('StatusBar.Style:', plugin.Style)
      console.log('Style keys:', Object.keys(plugin.Style))
    }
    
    return true
  } catch (error) {
    console.error(`❌ Error testing ${pluginName}:`, error)
    return false
  }
}

/**
 * 在控制台中运行完整的诊断
 */
export function runFullDiagnostic(): void {
  console.log('🔍 Starting Capacitor Full Diagnostic...')
  
  debugCapacitorEnvironment()
  
  const pluginsToTest = ['StatusBar', 'Filesystem', 'Haptics', 'Geolocation']
  pluginsToTest.forEach(pluginName => {
    testPlugin(pluginName)
  })
  
  console.log('\n🏁 Diagnostic Complete!')
}

// 在开发模式下自动运行诊断
if (process.env.NODE_ENV === 'development') {
  // 延迟执行，确保 Capacitor 环境已经初始化
  setTimeout(() => {
    try {
      runFullDiagnostic()
    } catch (error) {
      console.error('❌ Diagnostic failed:', error)
    }
  }, 3000) // 增加延迟时间，确保 Capacitor 完全初始化
}
