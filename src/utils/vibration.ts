/**
 * 振动工具函数
 * 统一处理 Capacitor Haptics 和原生 navigator.vibrate
 */

import { safeHaptics } from './capacitorUtils'
import { isApp } from '../config'
import log from './log'

/**
 * 振动模式枚举
 */
export enum VibrationPattern {
  // 短振动
  SHORT = 'short',
  // 中等振动
  MEDIUM = 'medium',
  // 长振动
  LONG = 'long',
  // 自定义振动
  CUSTOM = 'custom'
}

/**
 * 振动选项
 */
export interface VibrationOptions {
  // 振动模式
  pattern?: VibrationPattern
  // 自定义振动时长数组 [振动时长, 停止时长, 振动时长, ...]
  duration?: number[]
  // 单次振动时长（毫秒）
  singleDuration?: number
}

/**
 * 执行振动
 * @param options 振动选项
 */
export async function vibrate(options: VibrationOptions = {}): Promise<void> {
  const { pattern = VibrationPattern.SHORT, duration, singleDuration } = options

  try {
    if (isApp) {
      // 在 App 环境中使用 Capacitor Haptics
      const Haptics = await safeHaptics()
      if (Haptics) {
        switch (pattern) {
          case VibrationPattern.SHORT:
            await Haptics.impact({ style: 'light' })
            break
          case VibrationPattern.MEDIUM:
            await Haptics.impact({ style: 'medium' })
            break
          case VibrationPattern.LONG:
            await Haptics.impact({ style: 'heavy' })
            break
          case VibrationPattern.CUSTOM:
            // Haptics 不支持自定义时长，降级到原生 API
            if (duration && navigator.vibrate) {
              navigator.vibrate(duration)
            } else if (singleDuration && navigator.vibrate) {
              navigator.vibrate(singleDuration)
            }
            break
        }
        return
      }
    }

    // 降级到原生 navigator.vibrate
    if (navigator.vibrate) {
      if (duration) {
        navigator.vibrate(duration)
      } else if (singleDuration) {
        navigator.vibrate(singleDuration)
      } else {
        // 根据模式设置默认振动时长
        switch (pattern) {
          case VibrationPattern.SHORT:
            navigator.vibrate(200)
            break
          case VibrationPattern.MEDIUM:
            navigator.vibrate(400)
            break
          case VibrationPattern.LONG:
            navigator.vibrate(600)
            break
          default:
            navigator.vibrate(200)
        }
      }
    } else {
      log.warn('Vibration not supported on this device')
    }
  } catch (error) {
    log.error('Vibration failed:', error)
    
    // 最后的降级方案
    if (navigator.vibrate) {
      try {
        if (duration) {
          navigator.vibrate(duration)
        } else {
          navigator.vibrate(200)
        }
      } catch (fallbackError) {
        log.error('Fallback vibration also failed:', fallbackError)
      }
    }
  }
}

/**
 * 巡查提醒振动
 * 对应原来的 [500, 200, 500] 模式
 */
export async function vibratePatrolReminder(): Promise<void> {
  await vibrate({
    pattern: VibrationPattern.CUSTOM,
    duration: [500, 200, 500]
  })
}

/**
 * 报警振动
 * 对应原来的 [1300, 1100] 模式
 */
export async function vibrateAlarm(): Promise<void> {
  await vibrate({
    pattern: VibrationPattern.CUSTOM,
    duration: [1300, 1100]
  })
}

/**
 * 成功反馈振动
 */
export async function vibrateSuccess(): Promise<void> {
  await vibrate({
    pattern: VibrationPattern.SHORT
  })
}

/**
 * 错误反馈振动
 */
export async function vibrateError(): Promise<void> {
  await vibrate({
    pattern: VibrationPattern.LONG
  })
}

/**
 * 通知振动
 */
export async function vibrateNotification(): Promise<void> {
  await vibrate({
    pattern: VibrationPattern.MEDIUM
  })
}
