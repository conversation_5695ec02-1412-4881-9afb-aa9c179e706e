/**
 * 直接使用全局 Capacitor 对象的工具函数
 * 作为动态导入失败时的备用方案
 */

import { Platform } from 'quasar'
import log from './log'

// 获取全局 Capacitor 对象
function getCapacitor(): any {
  if (typeof window === 'undefined') {
    return null
  }
  return (window as any).Capacitor
}

// 获取 Capacitor 插件
function getCapacitorPlugin(pluginName: string): any {
  const capacitor = getCapacitor()
  if (!capacitor || !capacitor.Plugins) {
    return null
  }
  return capacitor.Plugins[pluginName]
}

/**
 * 直接获取 StatusBar 插件
 */
export function getStatusBarDirect() {
  if (!Platform.is.capacitor) {
    return null
  }

  const statusBar = getCapacitorPlugin('StatusBar')
  if (!statusBar) {
    log.warn('StatusBar plugin not found in Capacitor.Plugins')
    return null
  }

  return {
    StatusBar: statusBar,
    Style: statusBar.Style || {
      Light: 'LIGHT',
      Dark: 'DARK'
    }
  }
}

/**
 * 直接获取 Geolocation 插件
 */
export function getGeolocationDirect() {
  if (!Platform.is.capacitor) {
    return null
  }

  const geolocation = getCapacitorPlugin('Geolocation')
  if (!geolocation) {
    log.warn('Geolocation plugin not found in Capacitor.Plugins')
    return null
  }

  return { Geolocation: geolocation }
}

/**
 * 直接获取 LocalNotifications 插件
 */
export function getLocalNotificationsDirect() {
  if (!Platform.is.capacitor) {
    return null
  }

  const localNotifications = getCapacitorPlugin('LocalNotifications')
  if (!localNotifications) {
    log.warn('LocalNotifications plugin not found in Capacitor.Plugins')
    return null
  }

  return { LocalNotifications: localNotifications }
}

/**
 * 直接获取 TextToSpeech 插件
 */
export function getTextToSpeechDirect() {
  if (!Platform.is.capacitor) {
    return null
  }

  const textToSpeech = getCapacitorPlugin('TextToSpeech')
  if (!textToSpeech) {
    log.warn('TextToSpeech plugin not found in Capacitor.Plugins')
    return null
  }

  return { TextToSpeech: textToSpeech }
}

/**
 * 直接获取 Camera 插件
 */
export function getCameraDirect() {
  if (!Platform.is.capacitor) {
    return null
  }

  const camera = getCapacitorPlugin('Camera')
  if (!camera) {
    log.warn('Camera plugin not found in Capacitor.Plugins')
    return null
  }

  return {
    Camera: camera,
    CameraResultType: camera.CameraResultType || {},
    CameraSource: camera.CameraSource || {}
  }
}

/**
 * 直接获取 AppLauncher 插件
 */
export function getAppLauncherDirect() {
  if (!Platform.is.capacitor) {
    return null
  }

  const appLauncher = getCapacitorPlugin('AppLauncher')
  if (!appLauncher) {
    log.warn('AppLauncher plugin not found in Capacitor.Plugins')
    return null
  }

  return { AppLauncher: appLauncher }
}

/**
 * 列出所有可用的 Capacitor 插件
 */
export function listAvailablePlugins(): string[] {
  const capacitor = getCapacitor()
  if (!capacitor || !capacitor.Plugins) {
    return []
  }
  return Object.keys(capacitor.Plugins)
}
