import { BysMarker, Role, RolePermission, Permission, UserRole } from './dataStore'
import { StrPubSub } from 'ypubsub'
import { PlayAlarmAudio, PatrolReminderChanged } from '@utils/pubSubSubject'
import { point, distance } from 'turf'
import { isApp } from '@config/index'
import { i18n } from '@src/boot/i18n'
import { Store } from '@src/store'
import { ADMIN_ROLE_RID } from '@utils/constants'
import { Geolocation } from '@capacitor/geolocation'
import { Haptics, ImpactStyle } from '@capacitor/haptics'
import { LocalNotifications } from '@capacitor/local-notifications'
import log from '../utils/log'

// 巡查提醒监听器
let patrolReminderWatchId: string | null = null
let patrolReminderNotifiedSet: Set<string> | null = null

// 巡查提醒距离（米），后续可通过配置或参数自定义
let patrolReminderDistance = 15
// TODO: 可暴露 setPatrolReminderDistance 方法供外部设置

// 上一次定位点
let lastPosition: [number, number] | null = null
const minMoveDistance = 5 // 米

// 节流控制：两次检索最小间隔（毫秒）
const minCheckInterval = 3000
let lastCheckTime = 0

// 生成通知 ID
let notificationIdCounter = 1000
const getNextNotificationId = () => ++notificationIdCounter

// 通知权限状态
let notificationPermissionGranted = false

// 请求通知权限
async function requestNotificationPermission() {
  try {
    const permission = await LocalNotifications.requestPermissions()
    notificationPermissionGranted = permission.display === 'granted'
    log.info('Notification permission:', permission.display)
    return notificationPermissionGranted
  } catch (error) {
    log.warn('Failed to request notification permission:', error)
    return false
  }
}

// 判断当前用户是否有巡查提醒权限（NFC/维护管理员或超级管理员）
function hasPatrolReminderPermission(): boolean {
  const userRID = Store.state.UserRID
  // 1. 获取当前用户所有角色ID
  const myRoleIds = UserRole.getDataList()
    .filter(ur => ur.UserRID === userRID)
    .map(ur => ur.RoleRID)

  // 2. 获取所有与这些角色相关的 RolePermission
  const myRolePermissions = RolePermission.getDataList().filter(rp => myRoleIds.includes(rp.RoleRID))

  // 3. 判断是否有目标权限（cmd类型，Sys.NFCPatrol 或 Sys.Maintain）
  const hasPatrolPerm = myRolePermissions.some(rp => {
    if (!rp.PermissionRID) return false
    const perm = Permission.getData(rp.PermissionRID)
    return perm && perm.PermissionType === 'cmd' &&
      (perm.PermissionValue === 'Sys.NFCPatrol' || perm.PermissionValue === 'Sys.Maintain')
  })
  if (hasPatrolPerm) return true

  // 4. 是否为超级管理员（角色名为admin或角色RID为全0）
  const isSuperAdmin = Role.getDataList().some(role =>
    role.isMyRole && (role.RoleName === 'admin' || role.RID === ADMIN_ROLE_RID)
  )
  return isSuperAdmin
}

function stopPatrolReminderMonitor() {
  lastPosition = null
  patrolReminderNotifiedSet?.clear()
  if (patrolReminderWatchId !== null) {
    Geolocation.clearWatch({ id: patrolReminderWatchId })
    patrolReminderWatchId = null
  }
  patrolReminderNotifiedSet = null
}

function startPatrolReminderMonitor() {
  stopPatrolReminderMonitor()
  patrolReminderNotifiedSet = new Set<string>()

  const gotPosition = async (pos: GeolocationPosition) => {
    console.log('patrolReminderMonitor: got position', pos)
    const now = Date.now()
    if (now - lastCheckTime < minCheckInterval) {
      console.warn('patrolReminderMonitor: too frequent position checks, skipping this one')
      return
    }
    const accuracy = pos.coords.accuracy // 米为单位
    // 放宽精度要求，从30米调整到100米，因为室外GPS精度可能不够高
    if (accuracy > 100) {
      console.warn(`patrolReminderMonitor: position accuracy too low (${accuracy}m), skipping this check`)
      return
    }

    lastCheckTime = now
    const { longitude, latitude } = pos.coords
    console.log(`patrolReminderMonitor: current position - lat: ${latitude}, lng: ${longitude}, accuracy: ${accuracy}m`)
    const userPoint = point([longitude, latitude])
    // 检查与上一次定位的距离
    if (lastPosition) {
      const lastPoint = point(lastPosition)
      const moveDist = distance(userPoint, lastPoint, 'meters')
      console.log(`patrolReminderMonitor: moved ${moveDist}m since last check`)
      if (moveDist < minMoveDistance) {
        console.log(`patrolReminderMonitor: movement too small (${moveDist}m < ${minMoveDistance}m), skipping`)
        return
      }
    }
    lastPosition = [longitude, latitude]
    const markers = BysMarker.getDataList() || []
    console.log(`patrolReminderMonitor: checking ${markers.length} markers within ${patrolReminderDistance}m (temporarily increased for testing)`)

    // 收集所有在范围内且未提醒过的界桩
    const nearbyMarkers = markers.filter(marker => {
      if (marker.Lon && marker.Lat) {
        const markerPoint = point([marker.Lon, marker.Lat])
        const dist = distance(userPoint, markerPoint, 'meters')
        const rid = marker.RID ? String(marker.RID) : ''
        const isInRange = dist <= patrolReminderDistance
        const hasRid = !!rid
        const notNotified = patrolReminderNotifiedSet && !patrolReminderNotifiedSet.has(rid)

        if (isInRange) {
          console.log(`patrolReminderMonitor: marker ${marker.MarkerNo || rid} is ${dist.toFixed(1)}m away (${notNotified ? 'not notified' : 'already notified'})`)
        }

        return isInRange && hasRid && notNotified
      }
      return false
    })
    if (nearbyMarkers.length > 0) {
      console.log(`patrolReminderMonitor: found ${nearbyMarkers.length} nearby markers, triggering alerts`)

      // 记录已提醒
      nearbyMarkers.forEach(marker => {
        const rid = marker.RID ? String(marker.RID) : ''
        if (rid) patrolReminderNotifiedSet!.add(rid)
      })

      // 震动、铃声、合并通知
      // 使用 Capacitor Haptics 进行振动
      try {
        console.log('patrolReminderMonitor: triggering haptic feedback')
        await Haptics.impact({ style: ImpactStyle.Heavy })
        // 连续振动效果
        setTimeout(async () => {
          await Haptics.impact({ style: ImpactStyle.Medium })
        }, 200)
        setTimeout(async () => {
          await Haptics.impact({ style: ImpactStyle.Heavy })
        }, 700)
      } catch (error) {
        log.warn('Haptics vibration failed:', error)
        console.warn('patrolReminderMonitor: haptics failed, falling back to navigator.vibrate')
        // 降级到原生振动
        if (navigator.vibrate) navigator.vibrate([500, 200, 500])
      }

      // StrPubSub.publish(PlayAlarmAudio)
      const markerNos = nearbyMarkers.map(m => m.MarkerNo || m.RID).join(', ')

      // 使用 Capacitor LocalNotifications
      if (notificationPermissionGranted) {
        try {
          const notificationId = getNextNotificationId()
          console.log(`patrolReminderMonitor: sending notification (id: ${notificationId}) for markers: ${markerNos}`)

          await LocalNotifications.schedule({
            notifications: [{
              title: i18n.global.t('settingsPage.patrolReminder'),
              body: i18n.global.t('patrolReminder.nearbyMarker', { markerNo: markerNos }),
              id: notificationId,
              schedule: { at: new Date(Date.now() + 100) }, // 立即显示
              sound: undefined, // 使用默认声音
              attachments: undefined,
              actionTypeId: '',
              extra: null
            }]
          })
          console.log('patrolReminderMonitor: notification sent successfully')
        } catch (error) {
          log.warn('LocalNotifications failed:', error)
          console.warn('patrolReminderMonitor: notification failed:', error)
          // 这里可以添加降级处理，比如使用 Quasar 的 Notify
        }
      } else {
        log.warn('Notification permission not granted, skipping notification')
        console.warn('patrolReminderMonitor: notification permission not granted')
      }
    }
    // 离开所有界桩范围则清空已提醒集合
    const anyInRange = markers.some(marker => {
      if (marker.Lon && marker.Lat) {
        const markerPoint = point([marker.Lon, marker.Lat])
        const dist = distance(userPoint, markerPoint, 'meters')
        return dist <= patrolReminderDistance
      }
      return false
    })
    if (!anyInRange && patrolReminderNotifiedSet) patrolReminderNotifiedSet.clear()
  }

  const locationError = (err: any) => {
    log.warn('watchPosition error:', err)
    console.warn('patrolReminderMonitor: location error:', err)

    // 如果是权限相关错误，尝试重新初始化
    if (err && (err.code === 1 || err.message?.includes('permission'))) {
      console.warn('patrolReminderMonitor: location permission error, will retry in 10 seconds')
      setTimeout(() => {
        if (patrolReminderWatchId === null) {
          console.log('patrolReminderMonitor: retrying location monitoring after permission error')
          startPatrolReminderMonitor()
        }
      }, 10000)
    }
  }

  // 使用 @capacitor/geolocation 的 watchPosition
  patrolReminderWatchId = Geolocation.watchPosition({
    enableHighAccuracy: true,
    timeout: 5000,
    maximumAge: 0
  }, (position: GeolocationPosition | null, err?: any) => {
    if (position) {
      gotPosition(position)
    } else if (err) {
      locationError(err)
    }
  })
}

export function patrolReminderMonitor(enable: boolean) {
  if (enable) {
    startPatrolReminderMonitor()
  } else {
    stopPatrolReminderMonitor()
  }
}

export async function initPatrolReminderMonitor(enable: boolean) {
  const per = hasPatrolReminderPermission()
  if (!isApp || !per) return

  // ✅ 添加请求位置权限
  try {
    await Geolocation.requestPermissions({
      permissions: ['location', 'coarseLocation', 'background']
    })
  } catch (e) {
    log.warn('Request geolocation permission failed:', e)
  }

  // ✅ 请求通知权限
  await requestNotificationPermission()

  // 订阅设置变更事件
  StrPubSub.subscribe(PatrolReminderChanged, (val: boolean) => {
    patrolReminderMonitor(val)
  })
  // 首次初始化
  patrolReminderMonitor(enable)
}
