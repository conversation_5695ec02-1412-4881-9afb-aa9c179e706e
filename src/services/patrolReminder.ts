import { BysMarker, Role, RolePermission, Permission, UserRole } from './dataStore'
import { StrPubSub } from 'ypubsub'
import { PatrolReminderChanged } from '@utils/pubSubSubject'
import { scheduleLocalNotify } from '@utils/common'
import { point, distance } from 'turf'
import { isApp } from '@config/index'
import { i18n } from '@src/boot/i18n'
import { Store } from '@src/store'
import { ADMIN_ROLE_RID } from '@utils/constants'
import { safeGeolocation, safeLocalNotifications, safeTextToSpeech } from '@utils/capacitorUtils'
import { vibratePatrolReminder } from '@utils/vibration'
import log from '../utils/log'

// 巡查提醒监听器
let patrolReminderWatchId: string | null = null
let patrolReminderNotifiedSet: Set<string> | null = null

// 巡查提醒距离（米），后续可通过配置或参数自定义
let patrolReminderDistance = 15
// TODO: 可暴露 setPatrolReminderDistance 方法供外部设置

// 上一次定位点
let lastPosition: [number, number] | null = null
const minMoveDistance = 5 // 米

// 节流控制：两次检索最小间隔（毫秒）
const minCheckInterval = 3000
let lastCheckTime = 0

// 生成通知 ID
let notificationIdCounter = 1000
const getNextNotificationId = () => ++notificationIdCounter

// 通知权限状态
let notificationPermissionGranted = false

// 请求通知权限
async function requestNotificationPermission() {
  try {
    const LocalNotifications = await safeLocalNotifications()
    if (!LocalNotifications) return false

    const permission = await LocalNotifications.requestPermissions()
    notificationPermissionGranted = permission.display === 'granted'
    log.info('Notification permission:', permission.display)
    return notificationPermissionGranted
  } catch (error) {
    log.warn('Failed to request notification permission:', error)
    return false
  }
}

// 判断当前用户是否有巡查提醒权限（NFC/维护管理员或超级管理员）
function hasPatrolReminderPermission(): boolean {
  const userRID = Store.state.UserRID
  // 1. 获取当前用户所有角色ID
  const myRoleIds = UserRole.getDataList()
    .filter(ur => ur.UserRID === userRID)
    .map(ur => ur.RoleRID)

  // 2. 获取所有与这些角色相关的 RolePermission
  const myRolePermissions = RolePermission.getDataList().filter(rp => myRoleIds.includes(rp.RoleRID))

  // 3. 判断是否有目标权限（cmd类型，Sys.NFCPatrol 或 Sys.Maintain）
  const hasPatrolPerm = myRolePermissions.some(rp => {
    if (!rp.PermissionRID) return false
    const perm = Permission.getData(rp.PermissionRID)
    return perm && perm.PermissionType === 'cmd' &&
      (perm.PermissionValue === 'Sys.NFCPatrol' || perm.PermissionValue === 'Sys.Maintain')
  })
  if (hasPatrolPerm) return true

  // 4. 是否为超级管理员（角色名为admin或角色RID为全0）
  const isSuperAdmin = Role.getDataList().some(role =>
    role.isMyRole && (role.RoleName === 'admin' || role.RID === ADMIN_ROLE_RID)
  )
  return isSuperAdmin
}

async function stopPatrolReminderMonitor() {
  lastPosition = null
  patrolReminderNotifiedSet?.clear()
  if (patrolReminderWatchId !== null) {
    try {
      const Geolocation = await safeGeolocation()
      if (Geolocation) {
        Geolocation.clearWatch({ id: patrolReminderWatchId })
      }
    } catch (error) {
      // 降级到原生 geolocation
      navigator.geolocation.clearWatch(patrolReminderWatchId as unknown as number)
    }
    patrolReminderWatchId = null
  }
  patrolReminderNotifiedSet = null
}

async function startPatrolReminderMonitor() {
  stopPatrolReminderMonitor()
  patrolReminderNotifiedSet = new Set<string>()

  const gotPosition = async (pos: GeolocationPosition) => {
    const now = Date.now()
    if (now - lastCheckTime < minCheckInterval) {
      return
    }
    const accuracy = pos.coords.accuracy // 米为单位
    if (accuracy > 30) {
      return
    }

    lastCheckTime = now
    const { longitude, latitude } = pos.coords
    const userPoint = point([longitude, latitude])
    // 检查与上一次定位的距离
    if (lastPosition) {
      const lastPoint = point(lastPosition)
      const moveDist = distance(userPoint, lastPoint, 'meters')
      if (moveDist < minMoveDistance) {
        return
      }
    }
    lastPosition = [longitude, latitude]
    const markers = BysMarker.getDataList() || []

    // 收集所有在范围内且未提醒过的界桩
    const nearbyMarkers = markers.filter(marker => {
      if (marker.Lon && marker.Lat) {
        const markerPoint = point([marker.Lon, marker.Lat])
        const dist = distance(userPoint, markerPoint, 'meters')
        const rid = marker.RID ? String(marker.RID) : ''
        return dist <= patrolReminderDistance && rid && patrolReminderNotifiedSet && !patrolReminderNotifiedSet.has(rid)
      }
      return false
    })
    if (nearbyMarkers.length > 0) {
      // 记录已提醒
      nearbyMarkers.forEach(marker => {
        const rid = marker.RID ? String(marker.RID) : ''
        if (rid) patrolReminderNotifiedSet!.add(rid)
      })
      // 震动、铃声、合并通知
      await vibratePatrolReminder()
      const markerNos = nearbyMarkers.map(m => m.MarkerNo || m.RID).join(', ')
      if (notificationPermissionGranted) {
        try {
          const notificationId = getNextNotificationId()

          const notificationTitle = i18n.global.t('settingsPage.patrolReminder')
          const notificationText = i18n.global.t('patrolReminder.nearbyMarker', { markerNo: markerNos })

          await scheduleLocalNotify({
            id: notificationId,
            title: notificationTitle,
            text: notificationText,
          })

          // 使用文本转语音播报通知内容 (仅在移动端)
          if (isApp) {
            try {
              const TextToSpeech = await safeTextToSpeech()
              if (TextToSpeech) {
                // 检查TTS是否可用
                const isSupported = await TextToSpeech.isLanguageSupported({ lang: 'zh-CN' })
                if (!isSupported.supported) {
                  console.warn('patrolReminderMonitor: Chinese TTS not supported, trying default language')
                }

                const speechText = `${notificationTitle}，${notificationText}`
                console.log('patrolReminderMonitor: starting text-to-speech:', speechText)

                await TextToSpeech.speak({
                  text: speechText,
                  lang: isSupported.supported ? 'zh-CN' : 'en-US', // 中文优先，不支持则使用英文
                  rate: 1.0,     // 语速
                  pitch: 1.0,    // 音调
                  volume: 1.0,   // 音量
                  category: 'ambient'
                })
              }
            } catch (ttsError) {
              log.warn('Text-to-speech failed:', ttsError)
            }
          }
        } catch (error) {
          log.warn('scheduleLocalNotify failed:', error)
        }
      } else {
        log.warn('Notification permission not granted, skipping notification')
      }
    }
    // 离开所有界桩范围则清空已提醒集合
    const anyInRange = markers.some(marker => {
      if (marker.Lon && marker.Lat) {
        const markerPoint = point([marker.Lon, marker.Lat])
        const dist = distance(userPoint, markerPoint, 'meters')
        return dist <= patrolReminderDistance
      }
      return false
    })
    if (!anyInRange && patrolReminderNotifiedSet) patrolReminderNotifiedSet.clear()
  }

  const locationError = (err: any) => {
    log.warn('watchPosition error:', err)
    // 如果是权限相关错误，尝试重新初始化
    if (err && (err.code === 1 || err.message?.includes('permission'))) {
      setTimeout(() => {
        if (patrolReminderWatchId === null) {
          startPatrolReminderMonitor()
        }
      }, 10000)
    }
  }

  // 使用 @capacitor/geolocation 的 watchPosition
  try {
    const Geolocation = await safeGeolocation()
    if (Geolocation) {
      patrolReminderWatchId = Geolocation.watchPosition({
        enableHighAccuracy: true,
        timeout: 5000,
        maximumAge: 0
      }, (position: GeolocationPosition | null, err?: any) => {
        if (position) {
          gotPosition(position)
        } else if (err) {
          locationError(err)
        }
      })
    } 
  } catch (error) {
    log.warn('Failed to start geolocation watch:', error)
  }
}

export async function patrolReminderMonitor(enable: boolean) {
  if (enable) {
    await startPatrolReminderMonitor()
  } else {
    await stopPatrolReminderMonitor()
  }
}

export async function initPatrolReminderMonitor(enable: boolean) {
  const per = hasPatrolReminderPermission()
  if (!isApp || !per) return

  try {
    const Geolocation = await safeGeolocation()
    if (Geolocation) {
      await Geolocation.requestPermissions({
        permissions: ['location', 'coarseLocation', 'background']
      })
    }
  } catch (e) {
    log.warn('Request geolocation permission failed:', e)
  }

  await requestNotificationPermission()

  // 订阅设置变更事件
  StrPubSub.subscribe(PatrolReminderChanged, async (val: boolean) => {
    await patrolReminderMonitor(val)
  })
  // 首次初始化
  await patrolReminderMonitor(enable)
}
