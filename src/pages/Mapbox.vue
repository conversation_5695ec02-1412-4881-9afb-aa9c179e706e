<template>
  <section
    :id="mapId"
    class="mapbox-container"
    :ref="mapId"
  >
    <q-resize-observer @resize="onResize" />
    <!-- 自定义地图控件按钮 -->
    <q-btn
      ref="redLineControl"
      class="custom-control-btn red-line"
      :class="{ active: showRedLineGraph }"
      icon="timeline"
      flat
      size="sm"
      :ripple="false"
      @click="toggleRedLineGraph"
    />
    <q-btn
      v-if="isApp"
      ref="locateControl"
      class="custom-control-btn geolocation"
      :class="locateControlClasses"
      :icon="myIcon"
      flat
      size="sm"
      :ripple="false"
      @click="toggleTrackLocation()"
    />
    <!--地图中心十字架-->
    <div class="map-center-symbol"></div>
    <!-- 地图搜索/进度控制 -->
    <div ref="topLeftComponents">
      <div
        v-if="!isShowSpeedController"
        ref="searchWrapper"
        :class="isShowSearchBox"
        v-touch-swipe.horizontal="swipeMapSearchBlock"
      >
        <!-- 搜索条件 -->
        <div class="search-input-container">
          <div class="keyWord-input row no-wrap full-height">
            <q-input
              v-model="searchVal"
              type="search"
              ref="mapboxSearchInput"
              maxlength="64"
              borderless
              dense
              clearable
              class="q-pl-sm full-width col-grow map-search-input"
              :label="$t('maps.search')"
              :loading="searching"
              :debounce="500"
              @update:model-value="startSearch()"
              @keydown.enter="startSearch()"
            >
              <template v-slot:append>
                <q-btn-group
                  flat
                  unelevated
                  class="h-[40px]"
                >
                  <q-btn
                    unelevated
                    color="primary"
                    icon="search"
                    @click="startSearch()"
                    class="!w-full !h-full !inline-flex !border-none !px-1 search-btn"
                  ></q-btn>
                  <q-btn
                    unelevated
                    flat
                    color="primary"
                    class="!w-full !h-full !inline-flex !border-none !px-1 show-if-btn"
                    v-if="$q.screen.lt.sm"
                    :icon-right="showSearchBox ? 'first_page' : 'last_page'"
                    @click="searchWrapperOpenClick"
                  />
                </q-btn-group>

              </template>
            </q-input>
          </div>
        </div>

        <!-- 关键词搜索结果 -->
        <q-slide-transition>
          <q-list
            v-show="showKeyWordSearchResult"
            class="full-width proposal-container"
            bordered
            separator
            dense
          >
            <q-virtual-scroll
              :items="searchResult"
              separator
              :class="$q.screen.lt.sm
      ? 'proposal-container-virtual-scroll-mobile'
      : 'proposal-container-virtual-scroll'
      "
            >
              <template v-slot="{ item, index }">
                <q-item
                  :key="item.hotPointID + '--' + index"
                  class="proposal-item"
                  clickable
                  v-ripple
                  active-class="search-item-selected"
                  :active="index === currentSearchResultIndex"
                  @click="checkedKeyWordItem(item, index)"
                >
                  <q-item-section avatar>
                    <q-icon
                      size="20px"
                      color="blue-6"
                      name="iconfont bys-base-station"
                      v-if="item.cusType === 1"
                    />
                    <q-icon
                      size="20px"
                      :class="getIConColor(item.hotPointID)"
                      name="iconfont bys-jiezhuang"
                      v-else-if="item.cusType === 2"
                    />
                    <q-icon
                      size="20px"
                      color="grey-6"
                      name="search"
                      v-else
                    />
                  </q-item-section>
                  <q-item-section>
                    <template v-if="isEnUs">
                      <q-item-label>{{ item.ename || item.name }}</q-item-label>
                      <q-item-label caption>{{
      item.eaddress || item.address
    }}
                      </q-item-label>
                    </template>
                    <template v-else>
                      <q-item-label>{{ item.name }}</q-item-label>
                      <q-item-label caption>{{ item.address }}</q-item-label>
                    </template>
                  </q-item-section>
                  <q-item-section side>
                    <q-btn
                      flat
                      dense
                      round
                      icon="directions"
                      color="info"
                      @click="gotoDestination(item)"
                    />
                  </q-item-section>
                </q-item>
              </template>
            </q-virtual-scroll>
          </q-list>
        </q-slide-transition>
      </div>
      <div
        v-if="isShowSpeedController"
        class="speed-controllers"
      >
        <q-btn-group
          unelevated
          spread
          class="q-pa-none"
        >
          <q-btn
            dense
            flat
            icon="fast_rewind"
            :disable="playSpeed <= 1"
            @click="debSubSpeed"
          />
          <q-separator vertical />
          <q-btn
            dense
            flat
            icon="fast_forward"
            :disable="playSpeed >= 10"
            @click="debAddSpeed"
          />
          <q-separator vertical />
          <q-btn
            dense
            flat
            :icon="isPaused ? 'play_arrow' : 'pause'"
            @click="pauseOrPlay"
          />
          <q-separator vertical />
          <q-btn
            dense
            flat
            icon="cached"
            @click="replayTrackHistory"
          />
          <q-separator vertical />
          <q-btn
            dense
            flat
            icon="close"
            @click="closeShowTrackHistory"
          />
        </q-btn-group>
        <q-separator />
        <q-slider
          dense
          v-model="trackCurrentItemIndex"
          markers
          :min="0"
          :max="trackItemNum - 1"
        />
        <q-badge>
          {{ $t('tranckHisReplay.currentItem') }}:
          {{ trackCurrentItemIndex + 1 }} (1 to {{ trackItemNum }})
        </q-badge>
        <q-space />
        <q-badge>
          {{ $t('tranckHisReplay.speed') }}: {{ playSpeed }} (1 to 10)
        </q-badge>
      </div>
    </div>
    <q-page-sticky
      position="bottom-left"
      :offset="[10, 30]"
      class="alarm-marker-sticky"
      v-if="allAlarmMarkerData.length !== 0"
    >
      <q-badge
        color="negative"
        class="alarm-marker-badge"
        :label="allAlarmMarkerData.length"
      />
      <q-fab
        v-model="proxyShowHidden"
        icon="notification_important"
        color="orange"
        vertical-actions-align="left"
        direction="up"
        class="alarm-marker-fab"
      >
        <q-list
          bordered
          separator
          dense
          class="rounded-borders bg-white text-red overflow-auto w-max max-h-[400px]"
        >
          <template
            v-for="item in allAlarmMarkerData"
            :key="item.RID + ''"
          >
            <q-item
              clickable
              v-ripple
              class="!p-2"
              @click="showMarkerAlarm(item)"
            >
              <q-item-section>
                <q-item-label>{{ item.MarkerNo }}</q-item-label>
                <q-item-label caption>{{ alarmTime(item) }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </q-list>
      </q-fab>
    </q-page-sticky>
  </section>
</template>

<script lang="ts">
  import 'maplibre-gl/dist/maplibre-gl.css'
  import maplibreGl, { GeoJSONSource, LngLatLike, MapMouseEvent, MapTouchEvent, SourceSpecification } from 'maplibre-gl'
  import { lineDistance } from 'turf'
  import { defineComponent, defineAsyncComponent, createApp } from 'vue'
  import globalConfig, { appLang, isApp, webConfig } from '@src/config'
  import {
    CustomControl,
    CustomGeojsonFeature,
    CustomLayerSourcesV2,
    destroyLayerMarker,
    enableRedLineGraph,
    filterMarkerByShowLevel,
    flyTo,
    getAlarmLayerId,
    getLngLatFromBysMarker,
    initLayerMarker,
    loadBysMarkers,
    loadControllerMarker,
    loadRedLineGraphV2,
    MapInfoControl,
    MapStyleControl,
    MapStyleName,
    MyGeolocate,
    myLocation,
    pointFeatureMap2List,
    setMapStyleCursor,
    showRedLineGraph,
    syncDeviceLayerMarker,
    syncMapStyle,
    toggleRedLineGraphV2,
    useGaodeMapStyle,
  } from '@src/utils/map'
  import { StrPubSub } from 'ypubsub'
  import { awaitDataQueryFinish } from '@services/queryData'
  import { DataName } from '@store/data'
  import storage, { MapInfoKey } from '@utils/storage'
  import {
    checkBysMarkerIsAlarm,
    checkIs4GMarker,
    checkReportingIsPowerOn,
    isDisMarkerAlarmPopupWind,
  } from '@utils/common'
  import { Quasar, debounce, Platform } from 'quasar'
  import { GET_DATA, NS } from '@store/data/methodTypes'
  import { bysdb } from '@ygen/bysdb'
  import log from '@src/utils/log'
  import {
    BysMarkerDumpingAlarm,
    CleanAllAlarmMarkerData,
    Jump2Node,
    MarkerHasSomeAbnormal,
    QueryBysMarkerFinish,
    QueryControllerFinish,
    RemoveBysMarkerAlarm,
    ShowTrackHistory,
    ToggleMapStyleFinish,
    UpdateDbBysMarker,
    UpdateDbController,
    WakeupBaiduMap,
  } from '@utils/pubSubSubject'
  import { wrapperInvalidDate } from '@utils/dayjs'
  import packageInfo from '@app/package.json'
  import { dataLngLatChanged, getGCJ02LngLat, getLngLat, toGCJ02 } from '@utils/gcoord'
  import MapboxDraw from '@mapbox/mapbox-gl-draw'
  import '@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css'
  import { BysMarkerAndUpdateInfo } from '@utils/bysdb.type'
  import { DbName } from '@utils/permission'
  import { i18n } from '@boot/i18n'
  import { Store } from '@src/store'
  import { useBysMarkerData } from '@utils/vueUse/bysMarkerData'
  import GaoDePng from '@assets/GaoDe.png'
  // import BaiDuPng from '@assets/BaiDu.png'
  import { zhCNLocales, enUSLocales } from '@utils/maplibreLocals'
  import { safeGeolocation, safeAppLauncher } from '@utils/capacitorUtils'

  MapboxDraw.constants.classes.CONTROL_BASE = 'maplibregl-ctrl'
  MapboxDraw.constants.classes.CONTROL_PREFIX = 'maplibregl-ctrl-'
  MapboxDraw.constants.classes.CONTROL_GROUP = 'maplibregl-ctrl-group'

  // maplibreGl.accessToken = 'pk.eyJ1IjoibGluZmwiLCJhIjoiY2l2dTlxeWoyMDVrYjJ6bzFwemR6OWFoMCJ9.pppjfM7dDpXg1KVSaIZUIQ'
  // const tiandituKey = webConfig.map?.apk ?? ''
  const getGaodeMapStyle = useGaodeMapStyle()
  const { bysMarkerData } = useBysMarkerData()

  //PopupWindow存放档期那凸显的wind  其他分别存放各自的wind
  let PopupWindow,
    popupTrackWind: { [key: string]: any } = {}
  let LocalMap: maplibreGl.Map | undefined

  // 加载本地地图缓存
  Object.assign(webConfig.map, storage.fetch(MapInfoKey))
  let mapInfoCache = webConfig.map

  interface SearchResultItem {
    [key: string]: any

    hotPointID: string
    name: string
    address: string
    lonlat: string
    phone?: string
    ename?: string
    eaddress?: string
    // 自定义类型，主要为搜索结果添加图标。1：控制器 2：界桩：其他为默认搜索图标
    cusType?: number
  }

  enum WindEvt {
    BysMarkerAlarm = 'BysMarkerAl',
    BysMarkerLC = 'BysMarkerLC',
    ControllerLC = 'ControllerLC'
  }

  const BysMarkerAlComp = defineAsyncComponent(() => import('@components/bysMarkerPopup'))
  const ControllerLCComp = defineAsyncComponent(() => import('@components/controllerMarkerPopup'))
  const PopupEvt = {
    BysMarkerAl: {
      component: BysMarkerAlComp,
      popupWind: {},
    },
    BysMarkerLC: {
      component: BysMarkerAlComp,
      popupWind: {},
    },
    ControllerLC: {
      component: ControllerLCComp,
      popupWind: {},
    },
  }

  // key: WindEvt, value: rid
  const lastOpenPopupUidMap: Record<string, string> = {}

  enum MapAppName {
    baidu = 'com.baidu.BaiduMap',
    gaode = 'com.autonavi.minimap',
    tencent = 'com.tencent.map',
    google = 'com.google.android.apps.maps'
  }

  const appName = `andr.${packageInfo.appId}`

  enum coordType {
    wgs84 = 'wgs84',
    gcj02 = 'gcj02'
  }

  // enum baiduRouteMode {
  //   transit = 'transit',
  //   driving = 'driving',
  //   walking = 'walking',
  //   riding = 'riding'
  // }

  enum LocateMode {
    None = 0,
    Sticky,
    Unsticky
  }

  const myLocationRedLine = 'myLocationRedLine'

  let mapStyleType = mapInfoCache?.style ?? MapStyleName.Satellite
  syncMapStyle(mapStyleType)

  // 测距功能popup对象
  const measurePopup: { [key: string]: maplibreGl.Popup | undefined } = {}

  let isTouched = {
    targetRID: '',
    isTouched: false,
  }
  let positionWatchID: number
  let lastKeyWord: string

  const fullscreenControl = new maplibreGl.FullscreenControl()
  const navigationControl = new maplibreGl.NavigationControl()

  export default defineComponent({
    name: 'MapboxGL',
    data() {
      return {
        mapId: 'globalMapBox',
        mapStyleType,
        mapZoom: mapInfoCache?.zoom ?? 12,
        isMeasure: false,
        distance: {
          result: -1,
          // KM or M
          unit: 'KM',
        },
        // 搜索内容
        searchVal: '',
        // 搜索状态
        searching: false,
        // 搜索结果
        searchResult: [] as Array<SearchResultItem>,
        //是否显示search结果
        isShowPanel: true,
        // 选中的搜索结果项
        currentSearchResultItem: null as SearchResultItem | null,
        currentSearchResultIndex: -1,
        // 正在搜索的目标点，0为起点，1为终点
        searchPointStatus: 1,
        // 选中的路线
        selectedLine: null,
        // 选中路线的某段路线
        selectedSegment: null,
        // 上次选中的某路段索引序号
        lastSegmentIndex: -1,
        //适配移动端，是否显示searchBox
        showSearchBox: true,
        //缓存上一次标记的凸显
        lastMarker: {} as Record<string, any>,
        //存放所有的正在报警的界庄数据
        allAlarmMarkerData: [] as bysdb.IDbBysMarker[],
        //是否显示alarm寄存的弹出代理
        proxyShowHidden: false,
        //是否隐藏search，从而显示控制条
        isShowSpeedController: false,
        //是否暂停
        isPaused: false,
        //解析节点的模板
        trackColumns: [],
        //所有的节点
        trackSumPoint: [],
        //所有动画桢数
        trackItemNum: 0,
        //当前展示哪一个节点
        trackCurrentItemIndex: 0,
        //自动播放速度 (0-10)
        playSpeed: 5,
        //定时器句柄
        timerHandel: undefined,
        //禁止alarm事件的map fly
        isFlyDisabled: false,
        locateMode: LocateMode.None,
        trackLocationInterval: -1,
        myLocationLngLat: null as null | number[],
        showRedLineGraph,
      }
    },
    methods: {
      // 重置内置控件翻译
      updateBuiltinControlI18n() {
        if (!LocalMap) return
        Object.assign(LocalMap._locale, this.mapLocale)
        fullscreenControl._updateTitle()
        navigationControl._setButtonTitle(navigationControl._zoomInButton, 'ZoomIn')
        navigationControl._setButtonTitle(navigationControl._zoomOutButton, 'ZoomOut')
        navigationControl._setButtonTitle(navigationControl._compass, 'ResetBearing')
      },
      getIConColor(RID) {
        const marker: bysdb.DbBysMarker = bysMarkerData.value.find(item => item.RID === RID)
        let colorClass = marker.HasInstallStone ? (marker.HasInstallDevice ? 'online' : 'uninstall') : 'uninstall-stone'
        return colorClass
      },
      cleanAllAlarmMarkerData() {
        this.allAlarmMarkerData = []
        // 更新app我的位置红线图
        if (isApp) {
          this.renderMyLocationRedLineLayer()
        }
      },
      cleanMyLocationRedLineLayer() {
        if (LocalMap?.getLayer(myLocationRedLine)) {
          LocalMap?.removeLayer(myLocationRedLine)
        }
        if (LocalMap?.getSource(myLocationRedLine)) {
          LocalMap?.removeSource(myLocationRedLine)
        }
        StrPubSub.unsubscribe(
          ToggleMapStyleFinish,
          this.renderMyLocationRedLineLayer,
        )
      },
      async renderMyLocationRedLineLayer() {
        // 判断是否有报警数据，有则生成与报警界桩的红线
        if (this.allAlarmMarkerData.length === 0) {
          // 清除红线图层
          this.cleanMyLocationRedLineLayer()
          return
        }

        // 如果没有定位，则先定位
        if (this.myLocationLngLat === null) {
          return
        }

        // 生成线图层
        const lineStringFeatures: GeoJSON.FeatureCollection<GeoJSON.LineString,
          { [key: string]: any }> = {
          type: 'FeatureCollection',
          features: [],
        }
        for (let i = 0; i < this.allAlarmMarkerData.length; i++) {
          const device: BysMarkerAndUpdateInfo = this.allAlarmMarkerData[i]
          let lngLat = getLngLatFromBysMarker(device)
          const linestring: GeoJSON.Feature<GeoJSON.LineString,
            { [key: string]: any }> = {
            type: 'Feature',
            geometry: {
              type: 'LineString',
              coordinates: [this.myLocationLngLat, lngLat],
            },
            properties: {
              RID: device.RID,
            },
          }
          lineStringFeatures.features.push(linestring)
        }

        // 判断是否有该图层，有则更新，没有则添加
        const source = LocalMap?.getSource(myLocationRedLine) as GeoJSONSource | undefined
        if (source) {
          source.setData(lineStringFeatures)
        } else {
          // 添加图层
          const redLineSource: SourceSpecification = {
            type: 'geojson',
            data: lineStringFeatures,
          }
          LocalMap?.addSource(myLocationRedLine, redLineSource)
          LocalMap?.addLayer({
            id: myLocationRedLine,
            type: 'line',
            source: myLocationRedLine,
            layout: {
              'line-cap': 'round',
              'line-join': 'round',
            },
            paint: {
              'line-color': '#f00',
              'line-width': 1,
            },
          })
          StrPubSub.subscribe(
            ToggleMapStyleFinish,
            this.renderMyLocationRedLineLayer,
          )
        }
      },
      async connectToRedLineGraph() {
        if (isApp) {
          this.myLocationLngLat = await this.getCurrentPosition()
          StrPubSub.publish(
            'geolocation',
            'GetCurrentPosition',
            this.myLocationLngLat,
          )
        }
        this.myLocationLngLat && myLocation.addTo().setLngLat(this.myLocationLngLat)
        //需要居中
        if (this.locateMode === LocateMode.Sticky) {
          this.flyTo(this.myLocationLngLat as [number, number])
          StrPubSub.publish(
            'geolocation',
            'myLocation flyTo',
            this.myLocationLngLat,
          )
        }

        // 绘制我的位置与界桩红线图层
        this.renderMyLocationRedLineLayer()
      },
      getCurrentPosition(): Promise<number[]> {
        return new Promise(async (resolve, reject) => {
          try {
            const Geolocation = await safeGeolocation()
            if (!Geolocation) {
              // 降级到原生 geolocation
              navigator.geolocation.getCurrentPosition(
                (position) => {
                  resolve([position.coords.longitude, position.coords.latitude])
                },
                (error) => {
                  reject(error)
                },
                { enableHighAccuracy: true, timeout: 30000 }
              )
              return
            }

            const position = await Geolocation.getCurrentPosition({
              enableHighAccuracy: true,
              timeout: 30000,
            })
            // Capacitor返回的坐标同样为WGS84
            resolve(
              toGCJ02([position.coords.longitude, position.coords.latitude]),
            )
          } catch (error) {
            log.error('GetCurrentPosition onError', error)
            this.locateMode = LocateMode.None
            this.$q.notify({
              message: this.$t('message.locationFailed') as string,
              type: 'warning',
            })
            reject(error)
          }
        })
      },
      async watchPosition() {
        const onSuccess = position => {
          this.myLocationLngLat = toGCJ02([
            position.coords.longitude,
            position.coords.latitude,
          ])
          StrPubSub.publish('geolocation', 'myLocation', this.myLocationLngLat)
          myLocation.addTo().setLngLat(this.myLocationLngLat)
          //需要居中
          if (this.locateMode === LocateMode.Sticky) {
            this.flyTo(this.myLocationLngLat as [number, number])
            StrPubSub.publish(
              'geolocation',
              'myLocation flyTo',
              this.myLocationLngLat,
            )
          }
          this.renderMyLocationRedLineLayer()
        }

        const onError = error => {
          log.error('GetCurrentPosition onError', JSON.stringify(error))
          this.locateMode = LocateMode.None
          this.$q.notify({
            message: this.$t('message.locationFailed') as string,
            type: 'warning',
          })
        }

        // @capacitor/geolocation watchPosition 返回 Observable
        const Geolocation = await safeGeolocation()
        if (Geolocation) {
          positionWatchID = Geolocation.watchPosition({
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 0,
          }, (position, err) => {
            if (position) {
              onSuccess(position)
            } else if (err) {
              onError(err)
            }
          })
        } else {
          // 降级到原生 geolocation
          const options: PositionOptions = { enableHighAccuracy: true, maximumAge: 0, timeout: 5000 }
          positionWatchID = navigator.geolocation.watchPosition(
            (position) => onSuccess(position),
            (error) => onError(error),
            options
          ) as any
        }
      },
      async stopWatchPosition() {
        if (positionWatchID != null) {
          const Geolocation = await safeGeolocation()
          if (Geolocation) {
            Geolocation.clearWatch({ id: positionWatchID })
          } else {
            // 降级到原生 geolocation
            navigator.geolocation.clearWatch(positionWatchID as number)
          }
        }
        this.$nextTick(() => {
          this.cleanMyLocationRedLineLayer()
          myLocation.remove()
        })
      },
      toggleTrackLocation() {
        // 开启了界桩监控，则不允许切换定位模式
        if (this.patrolReminderWithApp && this.locateMode !== LocateMode.None) {
          this.locateMode = LocateMode.Sticky
          this.flyTo(this.myLocationLngLat as [number, number])
          return
        }
        /***
         * 点击第一次，进入到实时跟踪
         * 用户拖拽，推出实时跟踪
         *
         * 实时跟踪模式 (再次点击)--->推出定位
         */
        this.isFlyDisabled = true

        if (this.locateMode === LocateMode.None) {
          this.locateMode = LocateMode.Sticky
        } else if (this.locateMode === LocateMode.Sticky) {
          this.locateMode = LocateMode.None
          this.isFlyDisabled = false
        } else if (this.locateMode === LocateMode.Unsticky) {
          this.locateMode = LocateMode.Sticky
        }
      },

      alarmTime(data) {
        return wrapperInvalidDate(checkIs4GMarker(data.MarkerType) ? data?.markerInfo?.MarkerCmdTime : data?.updateInfo?.CmdTime)
      },
      gotoDestination(item) {
        this.currentSearchResultItem = item
        this.wakeBaiduMap(item)
      },

      // 使用@capacitor/app-launcher打开App
      async wakeMapsByUri(url: string): Promise<boolean> {
        try {
          const AppLauncher = await safeAppLauncher()
          if (!AppLauncher) {
            console.warn('AppLauncher not available')
            return false
          }

          await AppLauncher.openUrl({ url })
          return true
        } catch (err) {
          return false
        }
      },
      showMapList(item, grid = false) {
        this.$q
          .bottomSheet({
            grid,
            class: `${this.$q.platform.is.android ? 'q-show-android-dlg' : 'q-show-pc-dlg'}`,
            actions: [
              {
                label: this.$t('common.gaodeMap') as string,
                img: GaoDePng,
                id: 'gaode',
              },
              /*{
                label: this.$t('common.baiduMap') as string,
                img: BaiDuPng,
                id: 'baidu',
              },*/
              {
                label: this.$t('common.cancel') as string,
                icon: 'keyboard_arrow_down',
                id: 'cancel',
                classes: 'q-show-dlg-cancel-label',
              },
            ],
          })
          .onOk(action => {
            if (action.id === 'cancel') {
              return
            }
            // 给的是原始坐标，即默认的wgs84坐标
            const ret = item?.lonlat.split(' ')
            const lonLat = { lon: ret[0], lat: ret[1] }
            let webRUrl = ''
            let appUri = {
              appName: '',
              intent: {},
            }

            if (action.id === 'gaode') {
              // 高德地图，转换为gcj02坐标
              const gcj02LngLat = toGCJ02([lonLat.lon, lonLat.lat])
              lonLat.lon = gcj02LngLat[0]
              lonLat.lat = gcj02LngLat[1]
              // 接口地址: https://lbs.amap.com/api/uri-api/guide/mobile-web/point
              webRUrl = `https://uri.amap.com/marker?callnative=1&name=${item.name}&position=${lonLat.lon},${lonLat.lat}`
              appUri.appName = MapAppName.gaode
              appUri.intent = {
                action: 'ACTION_VIEW',
                category: 'CATEGORY_DEFAULT',
                package: MapAppName.gaode,
                type: 'text/css',
                // 接口地址: https://lbs.amap.com/api/amap-mobile/guide/android/marker/
                uri: `androidamap://viewMap?sourceApplication=appname&poiname=${item.name}&lat=${lonLat.lat}&lon=${lonLat.lon}&dev=0`,
                intentstart: 'startActivity',
              }
            } else if (action.id === 'baidu') {
              // 接口地址: https://lbsyun.baidu.com/index.php?title=uri/api/web
              webRUrl = `http://api.map.baidu.com/marker?output=html&mode=driving&title=${item.name}&content=${item.name}&location=${lonLat.lat},${lonLat.lon}&coord_type=${coordType.wgs84}&src=${appName}`
              appUri.appName = MapAppName.baidu
              appUri.intent = {
                action: 'ACTION_VIEW',
                category: 'CATEGORY_DEFAULT',
                package: MapAppName.baidu,
                type: 'text/css',
                // 接口地址: https://lbsyun.baidu.com/index.php?title=uri/api/android
                uri: `baidumap://map/marker?title=${item.name}&content=${item.name}&location=${lonLat.lat},${lonLat.lon}&coord_type=${coordType.wgs84}&src=${appName}`,
                flags: ['FLAG_ACTIVITY_CLEAR_TOP', 'FLAG_ACTIVITY_CLEAR_TASK'],
                intentstart: 'startActivity',
              }
            }

            if (Platform.is.cordova || Platform.is.capacitor) {
              this.wakeMapsByUri(appUri.intent.uri)
            } else {
              window.open(webRUrl)
            }
          })
      },

      // 唤醒百度地图
      wakeBaiduMap(item) {
        this.showMapList(item, false)
      },
      lonlatStrToArray(lonlat: string, separator = ','): number[] {
        return lonlat.split(separator).map(s => parseFloat(s))
      },
      flyTo(lngLat: LngLatLike) {
        flyTo(lngLat, this.mapZoom)
      },
      // 点击搜索结果选项，创建标记图层
      createKeyWordMarker(item: SearchResultItem) {
        const lngLat = this.lonlatStrToArray(item.lonlat, ' ') as LngLatLike
        this.flyTo(lngLat)
      },

      //恢复上一个点亮的marker的颜色
      resolveMarkerTextColor() {
        const marker = CustomLayerSourcesV2[this.lastMarker.type]?.get(this.lastMarker.id)
        if (!marker) {
          return
        }
        marker.properties.textColor = this.lastMarker.textColor

        let source: any = globalConfig.map?.getSource(this.lastMarker.type)
        const sourceData: GeoJSON.FeatureCollection<GeoJSON.Point> = {
          type: 'FeatureCollection',
          features: pointFeatureMap2List(CustomLayerSourcesV2[this.lastMarker.type]),
        }
        source?.setData(sourceData)
      },

      /**
       * 改变该marker的颜色
       * @param item
       * item.hotPointID为marker的ID
       * item.cusType: 1 2 用于区别bysMarker、controller
       */
      changeMarkerTextColor(item) {
        const markerType = {
          1: DataName.Controller,
          2: DataName.BysMarker,
        }
        const cusType = markerType[item.cusType]
        const featureMap: Map<string, CustomGeojsonFeature> | undefined = CustomLayerSourcesV2[cusType]
        const marker = featureMap?.get(item.hotPointID)
        if (!marker) {
          return
        }

        this.lastMarker = {
          id: item.hotPointID,
          type: cusType,
          textColor: marker.properties.textColor,
        }
        marker.properties.textColor = '#f00'
        const sourceData: GeoJSON.FeatureCollection<GeoJSON.Point> = {
          type: 'FeatureCollection',
          features: pointFeatureMap2List(featureMap),
        }
        const source = globalConfig.map?.getSource(cusType) as GeoJSONSource | undefined
        source?.setData(sourceData)
      },
      checkedKeyWordItem(item: SearchResultItem, index: number) {
        if (this.$q.screen.lt.sm) {
          this.isShowPanel = false
        }

        const dbName =
          item.cusType === 1 ? DbName.DbController : DbName.DbBysMarker
        StrPubSub.publish(Jump2Node, item.hotPointID, dbName)
        this.currentSearchResultItem = item
        this.currentSearchResultIndex = index
        //this.searchVal = item.name

        //高亮marker
        this.resolveMarkerTextColor()
        this.changeMarkerTextColor(item)
        this.createKeyWordMarker(item)
      },
      startSearchStatus() {
        this.searchResult = []
        this.searching = true
      },
      endSearchStatus() {
        this.searching = false
        this.currentSearchResultItem = null
        this.currentSearchResultIndex = -1
      },
      mySearchKeyWord(keyWord: string, ret: Array<SearchResultItem>) {
        const lowerCaseFilter = keyWord.toLowerCase()
        // 搜索界桩数据
        for (let i = 0; i < this.bysMarkerData.length; i++) {
          let item = this.bysMarkerData[i]
          const cond = [
            item.MarkerNo,
            item.MarkerHWID,
            item.MarkerDescription,
          ].join(',')
          const filterStr = cond.toLowerCase()
          if (filterStr.includes(lowerCaseFilter)) {
            ret.push({
              hotPointID: item.RID as string,
              name: item.MarkerNo as string,
              address: item.MarkerDescription as string,
              lonlat: getGCJ02LngLat(item).join(' '),
              cusType: 2,
            })
          }
        }
        // 搜索控制器数据
        for (let i = 0; i < this.controllerData.length; i++) {
          let item = this.controllerData[i]
          const cond = [
            item.ControllerNo,
            item.ControllerHWID,
            item.ControllerDescription,
          ].join(',')
          const filterStr = cond.toLowerCase()
          if (filterStr.includes(lowerCaseFilter)) {
            ret.push({
              hotPointID: item.RID as string,
              name: item.ControllerNo as string,
              address: item.ControllerDescription as string,
              lonlat: getGCJ02LngLat(item).join(' '),
              cusType: 1,
            })
          }
        }
      },
      async startSearch() {
        if (this.searching) {
          return
        }
        const keyWord: string = this.searchVal ? this.searchVal.trim() : ''
        if (!keyWord.length) {
          this.resolveMarkerTextColor()
          lastKeyWord = ''
          return
        }

        if (lastKeyWord !== keyWord) {
          lastKeyWord = keyWord
          this.startSearchStatus()
          this.mySearchKeyWord(keyWord, this.searchResult)
          this.endSearchStatus()
        }
        this.isShowPanel = true
      },
      // 切换红线图显示/隐藏
      toggleRedLineGraph() {
        enableRedLineGraph((this.showRedLineGraph = !this.showRedLineGraph))
        toggleRedLineGraphV2()
      },
      onResize() {
        LocalMap && LocalMap?.resize()
      },
      init(): void {
        globalConfig.map = LocalMap = new maplibreGl.Map({
          locale: this.mapLocale,
          ...this.mapOptions,
          // 下列参数不允许修改
          container: this.mapId,
          style: getGaodeMapStyle(this.mapStyleType, this.locale),
          attributionControl: false,
        })
        setMapStyleCursor()
        this.addController()
        this.listenMapEvents()
      },
      measure(e: any) {
        const geometry = e.features[0].geometry
        const geometryId = e.features[0].id
        //绘制的图形类型
        const type = geometry.type
        if (type === 'LineString') {
          //调用turf的距离计算方法，计算长度
          let result = lineDistance(geometry, 'kilometers')
          let unit = this.$t('maps.Kilometers')
          if (result < 1) {
            // 转换为米单位
            unit = this.$t('maps.meter')
            result = result * 1000
          }
          // 结果保留2位小数
          result = Number(result.toFixed(2))
          //获取折线对象的最后一个点坐标
          const coords = geometry.coordinates
          const popPosition = coords[coords.length - 1]

          //在该位置添加Popup弹框，显示测距结果
          let html = '<div class="distance-draw">'
          html += `<span class="distance-label">${this.$t(
            'maps.measure',
          )}</span>`
          html += `<span class="distance-result">${result}</span>`
          html += `<span class="distance-unit">${unit}</span>`
          html += '</div>'
          let popup = measurePopup[geometryId]
          if (!popup) {
            measurePopup[geometryId] = popup = new maplibreGl.Popup({
              closeOnClick: false,
            })
          }
          popup
            .setLngLat(popPosition)
            .setHTML(html)
            .addTo(LocalMap as maplibreGl.Map)
          popup.toggleClassName('line-popup')
        }
      },
      deleteMeasure(e: any) {
        const geometryId = e.features[0].id
        measurePopup[geometryId]?.remove()
        measurePopup[geometryId] = undefined
      },
      addController(): void {
        LocalMap!.addControl(fullscreenControl, 'top-right')
        LocalMap!.addControl(navigationControl, 'top-right')

        // 地图样式控件
        LocalMap?.addControl(new MapStyleControl({
          currentStyle: this.mapStyleType,
          onChanged: (currentStyle) => {
            this.mapStyleType = currentStyle
            syncMapStyle(currentStyle)
            this.saveMapInfo()
          },
        }), 'top-right')
        // 红线图
        LocalMap?.addControl(
          new CustomControl({
            buttons: [
              this.$refs.redLineControl.$el,
            ],
          }),
          'top-right',
        )

        const Draw = new MapboxDraw({
          //不允许使用键盘交互绘制
          keybindings: false,
          //设置为ture，可按住shift+拉框来拾取图形
          boxSelect: true,
          //点击要素时的响应半径（像素单位）
          clickBuffer: 5,
          //默认控件显示方式。如果设置为true，则会添加所有的绘图控件
          displayControlsDefault: false,
          touchEnabled: true,
          //添加指定的绘制控件
          controls: {
            //绘制线控件
            line_string: true,
            //绘制多边形控件
            // polygon: true,
            //删除图形控件
            trash: true,
          },
          styles: [
            // ACTIVE (being drawn)
            // line stroke
            {
              id: 'gl-draw-line',
              type: 'line',
              filter: [
                'all',
                ['==', '$type', 'LineString'],
                ['!=', 'mode', 'static'],
              ],
              layout: {
                'line-cap': 'round',
                'line-join': 'round',
              },
              paint: {
                'line-color': '#D20C0C',
                'line-dasharray': [0.2, 2],
                'line-width': 3,
              },
            },
            // INACTIVE (static, already drawn)
            // line stroke
            {
              id: 'gl-draw-line-static',
              type: 'line',
              filter: [
                'all',
                ['==', '$type', 'LineString'],
                ['==', 'active', 'false'],
              ],
              layout: {
                'line-cap': 'round',
                'line-join': 'round',
              },
              paint: {
                'line-color': '#027be3',
                'line-width': 4,
              },
            },

            // vertex point halos
            {
              id: 'gl-draw-polygon-and-line-vertex-halo-active',
              type: 'circle',
              filter: [
                'all',
                ['==', 'meta', 'vertex'],
                ['==', '$type', 'Point'],
                ['!=', 'mode', 'static'],
              ],
              paint: {
                'circle-radius': 8,
                'circle-color': '#FFF',
              },
            },
            // vertex points
            {
              id: 'gl-draw-polygon-and-line-vertex-active',
              type: 'circle',
              filter: [
                'all',
                ['==', 'meta', 'vertex'],
                ['==', '$type', 'Point'],
                ['!=', 'mode', 'static'],
              ],
              paint: {
                'circle-radius': 6,
                'circle-color': '#D20C0C',
              },
            },
          ],
        })
        LocalMap?.addControl(Draw, 'top-right')

        LocalMap?.on('draw.create', this.measure)
        LocalMap?.on('draw.update', this.measure)
        LocalMap?.on('draw.delete', this.deleteMeasure)
        LocalMap?.on('draw.selectionchange', e => {
          const geometryId = e.features[0]?.id
          if (!measurePopup[geometryId] || measurePopup[geometryId]?.isOpen()) {
            return
          }
          measurePopup[geometryId]?.addTo(LocalMap as maplibreGl.Map)
        })
        const drawBtn = document.querySelector('.mapbox-gl-draw_ctrl-draw-btn')
        // window.dr = Draw
        LocalMap?.on('draw.actionable', () => {
          //关闭之前的Popup
          if (PopupWindow !== undefined) {
            PopupWindow?.remove()
          }
          if (!drawBtn?.classList.contains('iconfont')) {
            drawBtn?.classList.add('iconfont')
            drawBtn?.classList.add('bys-measure')
          }
          if (Draw.getAll().features.length === 0) {
            //没有选点就结束了,所以只需要移除样式
            if (drawBtn?.classList.contains('draw-btn-active')) {
              drawBtn?.classList.remove('draw-btn-active')
            }
          } else {
            drawBtn?.classList.toggle('draw-btn-active')
          }
        })

        // 地图缩放级别控件
        LocalMap!.addControl(new MapInfoControl(), 'bottom-left')

        // 地图搜索控件/播放速度控制控件
        LocalMap?.addControl(
          new CustomControl({
            buttons: [this.$refs.topLeftComponents],
          }),
          'top-left',
        )

        // 定位控件
        if (isApp) {
          LocalMap?.addControl(
            new CustomControl({
              buttons: [this.$refs.locateControl.$el],
            }),
            'top-right',
          )
        } else if (this.$q.platform.is.android) {
          // Add geolocate control to the map.
          // 移动端浏览器定位控件
          const myGeolocator = new MyGeolocate({
            positionOptions: {
              enableHighAccuracy: true,
            },
            trackUserLocation: true,
          })
          LocalMap?.addControl(myGeolocator, 'top-right')
          // @ts-ignore
          // eslint-disable-next-line no-undef
          myGeolocator.on('geolocate', (position: Position) => {
            this.myLocationLngLat = [
              position.coords.longitude,
              position.coords.latitude,
            ]
            //主动画红线
            this.renderMyLocationRedLineLayer()
          })
        }
      },
      bysMarkerLeftClick(event: MapMouseEvent | MapTouchEvent) {
        event.preventDefault()
        const alarmId = getAlarmLayerId(DataName.BysMarker)
        const layers: string[] = [DataName.BysMarker]
        if (LocalMap?.getLayer(alarmId)) {
          layers.push(alarmId)
        }

        const features:
          | maplibreGl.MapboxGeoJSONFeature[]
          | undefined = LocalMap?.queryRenderedFeatures(event.point, { layers })
        if (!features || !features.length) {
          return
        }

        const feature: maplibreGl.MapboxGeoJSONFeature = features[0]
        const { properties, geometry } = feature
        const RID = properties?.RID ?? ''
        const coordinates: any = (geometry as GeoJSON.Point).coordinates

        this.popupBysMarkerWind(
          RID,
          coordinates || event.lngLat.toArray(),
          WindEvt.BysMarkerLC,
        )
      },
      controllerMarkerLeftClick(event: MapMouseEvent | MapTouchEvent) {
        event.preventDefault()
        const features:
          | maplibreGl.MapboxGeoJSONFeature[]
          | undefined = LocalMap?.queryRenderedFeatures(event.point, {
            layers: [DataName.Controller],
          })
        if (!features || !features.length) {
          return
        }

        const feature: maplibreGl.MapboxGeoJSONFeature = features[0]
        const { properties, geometry } = feature
        const RID = properties?.RID ?? ''
        const coordinates: any = (geometry as GeoJSON.Point).coordinates
        this.popupBysMarkerWind(
          RID,
          coordinates || event.lngLat.toArray(),
          WindEvt.ControllerLC,
        )
      },
      bysMarkerTouch(feature: GeoJSON.Feature<GeoJSON.Point, any>, lngLat) {
        const { properties, geometry } = feature
        const RID = properties?.RID ?? ''
        const coordinates: any = geometry.coordinates

        this.popupBysMarkerWind(
          RID,
          coordinates || lngLat.toArray(),
          WindEvt.BysMarkerLC,
        )
      },
      controllerTouch(feature: GeoJSON.Feature<GeoJSON.Point, any>, lngLat) {
        const { properties, geometry } = feature
        const RID = properties?.RID ?? ''
        const coordinates: any = (geometry as GeoJSON.Point).coordinates
        this.popupBysMarkerWind(
          RID,
          coordinates || lngLat.toArray(),
          WindEvt.ControllerLC,
        )
      },
      mapOnClick() {
        this.$refs.mapboxSearchInput.blur()
        this.isShowPanel = false
        this.proxyShowHidden = false
      },
      swipeMapSearchBlock({ ...info }) {
        if (info.touch && info.direction === 'left') {
          this.showSearchBox = !this.showSearchBox
        }
      },
      searchWrapperOpenClick() {
        this.showSearchBox = !this.showSearchBox
        this.isShowPanel = this.showSearchBox
      },
      onceStyleLoad() {
        this.updateBuiltinControlI18n()
        // 初始化控制器、界桩Marker图层，在请求完对应的数据后，生成Marker标记
        initLayerMarker(DataName.Controller)
          .then(() => awaitDataQueryFinish(DataName.Controller, QueryControllerFinish))
          .then(() => loadControllerMarker())
          .catch(err => {
            log.error('初始化控制器图层失败:', err)
          })

        initLayerMarker(DataName.BysMarker)
          .then(() => awaitDataQueryFinish(DataName.BysMarker, QueryBysMarkerFinish))
          .then(() => {
            loadBysMarkers()
            // 初始化红线图
            loadRedLineGraphV2()
          })
          .catch(err => {
            log.error('初始化界桩图层失败:', err)
          })

        // app默认在地图加载后定位一次
        /*if (this.$q.platform.is.cordova) {
            this.$refs.locateControl?.$el.click?.()
          }*/
      },
      mapOnZoom() {
        this.mapZoom = LocalMap?.getZoom()
        filterMarkerByShowLevel(this.mapZoom)
        filterMarkerByShowLevel(this.mapZoom, DataName.Controller)
      },
      markerTouch(event: MapTouchEvent, isBysMarker = true) {
        // event.preventDefault()
        const alarmId = getAlarmLayerId(
          isBysMarker ? DataName.BysMarker : DataName.Controller,
        )
        const layers: string[] = [
          isBysMarker ? DataName.BysMarker : DataName.Controller,
        ]
        if (LocalMap?.getLayer(alarmId)) {
          layers.push(alarmId)
        }
        const features = LocalMap?.queryRenderedFeatures(event.point, {
          layers,
        }) as Array<GeoJSON.Feature<GeoJSON.Point, any>>
        if (features.length === 0) {
          return
        }
        const feature: GeoJSON.Feature<GeoJSON.Point, any> = features[0]
        const { properties, geometry } = feature
        const RID = properties?.RID ?? ''
        const coordinates: any = geometry.coordinates

        if (isTouched.isTouched) {
          if (isTouched.targetRID === RID) {
            //点击到此处
            isBysMarker
              ? this.bysMarkerTouch(feature, coordinates)
              : this.controllerTouch(feature, coordinates)
          } else {
            isTouched = {
              targetRID: '',
              isTouched: false,
            }
          }
        } else {
          isTouched = {
            targetRID: RID,
            isTouched: true,
          }
          setTimeout(() => {
            isTouched = {
              targetRID: '',
              isTouched: false,
            }
          }, 600)
        }
      },
      // 监听地图各类事件
      listenMapEvents() {
        //监听解除报警的指令
        LocalMap?.once('load', this.onceStyleLoad)
        LocalMap?.on('click', this.mapOnClick)
        LocalMap?.on('touchstart', this.mapOnClick)

        LocalMap?.on('zoom', this.mapOnZoom)
        LocalMap?.on('move', this.saveMapInfo)

        LocalMap?.on('dblclick', DataName.BysMarker, this.bysMarkerLeftClick)
        LocalMap?.on(
          'dblclick',
          getAlarmLayerId(DataName.BysMarker),
          this.bysMarkerLeftClick,
        )
        LocalMap?.on('touchstart', DataName.BysMarker, (event: MapTouchEvent) => {
          this.markerTouch(event, true)
        })
        LocalMap?.on(
          'touchstart',
          getAlarmLayerId(DataName.BysMarker),
          (event: MapTouchEvent) => {
            this.markerTouch(event, true)
          },
        )

        LocalMap?.on(
          'dblclick',
          DataName.Controller,
          this.controllerMarkerLeftClick,
        )
        LocalMap?.on(
          'touchstart',
          DataName.Controller,
          (event: MapTouchEvent) => {
            this.markerTouch(event, false)
          },
        )
        LocalMap?.on('drag', () => {
          //设置切换状态
          //取消屏幕跟踪
          if (this.locateMode !== LocateMode.None) {
            this.locateMode = LocateMode.Unsticky
          }
        })
      },
      saveMapInfo() {
        if (!LocalMap) {
          return
        }
        // 缓存地图数据
        storage.save(MapInfoKey, {
          zoom: LocalMap?.getZoom(),
          center: LocalMap?.getCenter(),
          style: this.mapStyleType,
        })
      },

      removeAlarm(data: bysdb.IDbBysMarker) {
        //解除报警
        this.allAlarmMarkerData = this.allAlarmMarkerData.filter(
          item => item.RID !== data.RID,
        )
        PopupWindow?.remove()
        PopupEvt['BysMarkerAl'].popupWind?.unmount?.()
        // 更新app我的位置红线图
        if (isApp) {
          this.renderMyLocationRedLineLayer()
        }
      },

      //接收到界桩信号之后，弹窗popup
      popupWindRecvBysMarkerSignal(data: BysMarkerAndUpdateInfo) {
        // 4g界桩的开机上报的指令，不作为报警处理
        if (checkIs4GMarker(data.MarkerType)) {
          if (checkReportingIsPowerOn(data.markerInfo ?? {}, true)) return
        }

        //已经存在的元素，不需要重复push
        if (!this.allAlarmMarkerData.find(item => item.RID === data.RID) && checkBysMarkerIsAlarm(data)) {
          this.allAlarmMarkerData.push(data)
        }
        let lngLat = getLngLatFromBysMarker(data)
        this.popupBysMarkerWind(data.RID, lngLat, WindEvt.BysMarkerAlarm)
        // 更新app我的位置红线图
        if (isApp) {
          this.renderMyLocationRedLineLayer()
        }
      },
      /**
       * 1.若存不在vue组件->new [popup, vue]  存在则跳过
       * 2.设置Props
       * 3.强制更新vue组件
       * @param rid  弹窗中的组件RID
       * @param lngLat   弹窗经纬度
       * @param popupEvt    是哪一个事件引发的弹窗
       * @param isCloseOnClick 默认为需要手动关闭弹窗
       */
      popupBysMarkerWind(rid, lngLat, popupEvt, isCloseOnClick = false) {
        /***
         * 1.报警事件触发 &
         * 2.上一个popup没有关闭 &
         * 3.上一个事件是报警事件 ===>
         * 不弹窗
         */
        if (
          popupEvt === WindEvt.BysMarkerAlarm &&
          PopupWindow?.isOpen() &&
          PopupWindow === PopupEvt[popupEvt].popupWind.$popup
        ) {
          return
        }
        // 主动禁止弹窗，对应的弹窗设置没有设置上
        if (popupEvt === WindEvt.BysMarkerAlarm && isDisMarkerAlarmPopupWind()) {
          return
        }
        // 如果是报警事件，而且此时正在导航/显示历史轨迹，则禁用alarm的跳转
        if (popupEvt === WindEvt.BysMarkerAlarm && this.isFlyDisabled) {
          return
        }

        // 界桩报警与正常时一样使用同一个popup组件，修正popupEvt属性
        if (popupEvt === WindEvt.BysMarkerAlarm) {
          popupEvt = WindEvt.BysMarkerLC
        }

        LocalMap?.doubleClickZoom.disable()
        window.setTimeout(() => {
          LocalMap?.doubleClickZoom.enable()
        })

        // if (lastOpenPopupUidMap[popupEvt] === rid) {
        //   const dbMarker = BysMarker.getData(rid) as BysMarkerAndUpdateInfo
        //   if (checkBysMarkerIsAlarm(dbMarker)) {
        //     this.flyTo(lngLat)
            // if (!PopupEvt[popupEvt].popupWind.$popup.isOpen()) {
            //   PopupEvt[popupEvt].popupWind.$popup.addTo(LocalMap)
            // }
            // return
          // }
        // }

        //关闭之前的Popup
        PopupWindow?.remove()
        PopupEvt[popupEvt].popupWind?.unmount?.()
        const vueComponent = PopupEvt[popupEvt]?.component
        if (!vueComponent) { return }

        lastOpenPopupUidMap[popupEvt] = rid
        const domFragment = document.createElement('div')
        domFragment.id = `popup-${popupEvt}-${rid}`
        domFragment.classList.add(popupEvt)
        // mounted vue app to popup html
        const popupApp = PopupEvt[popupEvt].popupWind = createApp(vueComponent, {
          RID: rid,
        })
        popupApp.use(Quasar, {})
        popupApp.use(i18n)
        popupApp.use(Store)
        popupApp.mount(domFragment)

        const popupOption: maplibreGl.PopupOptions = {
          closeButton: !isCloseOnClick,
          closeOnClick: isCloseOnClick,
        }
        const popup = new maplibreGl.Popup(popupOption)
          .setLngLat(lngLat)
          .setDOMContent(domFragment)
          .addTo(LocalMap as maplibreGl.Map)

        // @ts-ignore
        popupApp.$popup = popup
        PopupWindow = popup
        PopupWindow.$rid = rid
        // 当前只存在一个报警界桩 则跳转至报警界桩
        if (this.allAlarmMarkerData.length === 1) {
          this.flyTo(lngLat)
        }
      },

      showMarkerAlarm(item: BysMarkerAndUpdateInfo) {
        if (this.$q.platform.is.mobile) {
          this.proxyShowHidden = false
        }
        let lngLat = getLngLatFromBysMarker(item)
        this.popupBysMarkerWind(item.RID, lngLat, WindEvt.BysMarkerLC)
      },

      //初始化轨迹历史动画需要数resolveMarkerTextColor据资源
      initTrackHisAnimateData() {
        this.trackCurrentItemIndex = 0
        this.trackItemNum = this.trackSumPoint.length
      },

      //将所有的点画在map，并且实线连接
      initTrackHisIntoMapbox() {
        let route: Array<number[]> = []
        let points: GeoJSON.Feature<GeoJSON.Point>[] = []

        for (let item of this.trackSumPoint) {
          let coordinate = getGCJ02LngLat(item)
          route.push(coordinate)
          points.push({
            type: 'Feature',
            properties: {},
            geometry: {
              type: 'Point',
              coordinates: coordinate as GeoJSON.Position,
            },
          })
        }

        //线
        LocalMap?.addSource('trackRoute', {
          type: 'geojson',
          data: {
            type: 'Feature',
            properties: {},
            geometry: {
              type: 'LineString',
              coordinates: route,
            },
          },
        })
        LocalMap?.addLayer({
          id: 'trackRoute',
          type: 'line',
          source: 'trackRoute',
          layout: {
            'line-join': 'round',
            'line-cap': 'round',
          },
          paint: {
            'line-color': '#3bb2d0',
            'line-width': 2,
          },
        })

        //点
        LocalMap?.addSource('trackPoints', {
          type: 'geojson',
          data: {
            type: 'FeatureCollection',
            features: points,
          },
        })
        LocalMap?.addLayer({
          id: 'trackPoints',
          type: 'circle',
          source: 'trackPoints',
          paint: {
            'circle-color': '#3575d3',
          },
        })

        this.flyTo(route[0] as maplibreGl.LngLatLike)
        //当更新图层，就重新渲染图层界面
        StrPubSub.subscribe(ToggleMapStyleFinish, this.initTrackHisIntoMapbox)
      },

      showTrackHistory(trackSum: bysdb.IDbMarkerHistory[], columns) {
        //禁止报警跳转
        this.isFlyDisabled = true
        //如果上一次打开的没有结束/关闭，则清除上一次的数据，接触定时器，清除mapSource、mapLayout
        this.clearMapSourceLay()
        this.clearTimer()

        //设置标志，隐藏search，显示进度控制条
        this.isShowSpeedController = true
        this.isPaused = false
        //初始化数据
        this.trackColumns = columns
        this.trackSumPoint = trackSum
        this.initTrackHisAnimateData()

        //初始化图层
        this.initTrackHisIntoMapbox()
        //设置定时器，开始自动播放
        this.TrackHisPlay()
      },
      //设置定时器，开始自动播放
      TrackHisPlay() {
        this.timerHandel = setInterval(
          this.animateTrackHis,
          5000 / this.playSpeed,
        )
      },
      //动画播放轨迹历史
      animateTrackHis() {
        //播放结束
        if (this.trackCurrentItemIndex < this.trackSumPoint.length - 1) {
          this.trackCurrentItemIndex++
        } else {
          this.clearTimer()
          this.isPaused = true
        }
      },
      //暂停/继续播放
      pauseOrPlay() {
        this.isPaused = !this.isPaused
        //销毁 / 新建计时器
        if (this.isPaused === true) {
          //已经停止，销毁计时器
          this.clearTimer()
        } else {
          //为了兼容某些不可预期的错误
          this.clearTimer()
          this.TrackHisPlay()
        }
      },
      //关闭界庄报警轨迹历史展示
      closeShowTrackHistory() {
        //销毁定时器
        this.clearTimer()
        //销毁图层
        this.clearMapSourceLay()
        this.isShowSpeedController = false

        //打开报警跳转
        this.isFlyDisabled = false
        StrPubSub.unsubscribe(ToggleMapStyleFinish, this.initTrackHisIntoMapbox)
      },
      //重播
      replayTrackHistory() {
        //current数据清0
        this.trackCurrentItemIndex = 0
        this.isPaused = false
        this.clearTimer()
        this.TrackHisPlay()
      },
      //加快播放速度
      addSpeed() {
        this.playSpeed++
      },
      //减慢播放速度
      subSpeed() {
        this.playSpeed--
      },
      clearMapSourceLay() {
        //重置资源
        popupTrackWind.$popup?.remove()
        popupTrackWind = {}
        if (LocalMap?.getLayer('trackRoute')) {
          LocalMap?.removeLayer('trackRoute')
        }
        if (LocalMap?.getSource('trackRoute')) {
          LocalMap?.removeSource('trackRoute')
        }

        if (LocalMap?.getLayer('trackPoints')) {
          LocalMap?.removeLayer('trackPoints')
        }
        if (LocalMap?.getSource('trackPoints')) {
          LocalMap?.removeSource('trackPoints')
        }
      },
      clearTimer() {
        clearInterval(this.timerHandel)
      },

      createTrackHisDom(trackData: bysdb.IDbMarkerHistory): string[] {
        let domArray: string[] = []
        for (let li of this.trackColumns) {
          domArray.push(
            `<li>${li.label}:${li.format
              ? li.format(trackData[li.field], trackData)
              : trackData[li.field]
            }</li>`,
          )
        }
        return domArray
      },
      //轨迹回放的弹窗
      //如果popup不存在，就创建新的popup，否则直接强制修改popup的props和lanlat，更新视图
      popupTrackHisWind(trackData: bysdb.IDbMarkerHistory) {
        LocalMap?.doubleClickZoom.disable()
        window.setTimeout(() => {
          LocalMap?.doubleClickZoom.enable()
        })

        if (!popupTrackWind.$popup) {
          const popupOption: maplibreGl.PopupOptions = {
            closeButton: true,
            closeOnClick: false,
          }
          popupTrackWind.$popup = new maplibreGl.Popup(popupOption)
            .setLngLat(getGCJ02LngLat(trackData) as LngLatLike)
            .setHTML(
              `<div class="track-his-popupWind"><ul>${this.createTrackHisDom(
                trackData,
              ).join(' ')}</ul></div>`,
            )
            .addTo(LocalMap as maplibreGl.Map)
        } else {
          popupTrackWind.$popup?.remove()
          popupTrackWind.$popup.setHTML(
            `<div class="track-his-popupWind"><ul>${this.createTrackHisDom(
              trackData,
            ).join(' ')}</ul></div>`,
          )
          popupTrackWind.$popup.setLngLat([
            trackData.Lon as number,
            trackData.Lat as number,
          ])
          popupTrackWind.$popup.addTo(LocalMap as maplibreGl.Map)
        }
      },
      checkMarkerPopupPosition(data: bysdb.IDbBysMarker, oldData: bysdb.IDbBysMarker) {
        // 位置没有变化
        if (!dataLngLatChanged(getLngLat(data), getLngLat(oldData))) {
          return
        }
        // popup 没有打开
        if (!PopupWindow?.isOpen() || PopupWindow?.$rid !== data.RID) {
          return
        }
        // 更新popup内容
        this.popupBysMarkerWind(data.RID, getGCJ02LngLat(data), WindEvt.BysMarkerLC, false)
      },
      checkControllerPopupPosition(data: bysdb.IDbController, oldData: bysdb.IDbController) {
        // 位置没有变化
        if (!dataLngLatChanged(getLngLat(data), getLngLat(oldData))) {
          return
        }
        // popup 没有打开，或者不是当前的数据
        if (!PopupWindow?.isOpen() || PopupWindow?.$rid !== data.RID) {
          return
        }
        // 更新popup内容
        this.popupBysMarkerWind(data.RID, getGCJ02LngLat(data), WindEvt.ControllerLC, false)
      },
      changeDeviceMarkerState(dbMarker: BysMarkerAndUpdateInfo) {
        syncDeviceLayerMarker(dbMarker)
      }
    },
    watch: {
      '$i18n.locale'() {
        this.updateBuiltinControlI18n()
      },
      async locateMode(newVal, oldVal) {
        if (oldVal === LocateMode.None) {
          //开启定时器，获取gps
          /*this.connectToRedLineGraph()
            this.trackLocationInterval = window.setInterval(this.connectToRedLineGraph, 6000)*/

          await this.watchPosition()
        }
        if (newVal === LocateMode.None) {
          /*//关闭定时器,取消固定，删除feature，终止
            clearInterval(this.trackLocationInterval)
            //清楚图层
            this.cleanMyLocationRedLineLayer()
            myLocation.remove()*/

          await this.stopWatchPosition()
        } else if (newVal === LocateMode.Sticky) {
          //强制执行一次
          this.connectToRedLineGraph()
          //fly
          this.flyTo(this.myLocationLngLat)
        }
      },
      searchVal(val) {
        if (!val) {
          this.searchResult = []
          this.endSearchStatus()
        }
      },
      trackCurrentItemIndex() {
        //更新popup
        this.popupTrackHisWind(this.trackSumPoint[this.trackCurrentItemIndex])
      },
      playSpeed() {
        //更改了播放速度
        this.clearTimer()
        this.TrackHisPlay()
        this.isPaused = false
      },
      patrolReminderWithApp: {
        immediate: true,
        handler(val) {
          if (val) {
            this.toggleTrackLocation()
          }
        }
      }
    },
    computed: {
      mapLocale() {
        return this.$i18n.locale === appLang.zhCN ? zhCNLocales : enUSLocales
      },
      bysMarkerData() {
        return bysMarkerData.value
      },
      isApp() {
        return isApp
      },
      locateControlClasses() {
        return {
          /*'track-location': this.locateMode === LocateMode.Sticky,
            'once-location': this.locateMode === LocateMode.Unsticky,*/
          'on-location':
            this.locateMode === LocateMode.Sticky ||
            this.locateMode === LocateMode.Unsticky,
          'dis-location': this.locateMode === LocateMode.None,
        }
      },
      myIcon() {
        if (
          this.locateMode === LocateMode.Sticky ||
          this.locateMode === LocateMode.None
        ) {
          return 'gps_fixed'
        }
        if (this.locateMode === LocateMode.Unsticky) {
          return 'gps_not_fixed'
        }
        return 'gps_fixed'
      },
      debAddSpeed() {
        return debounce(this.addSpeed, 200)
      },
      debSubSpeed() {
        return debounce(this.subSpeed, 200)
      },
      isShowSearchBox() {
        return {
          'search-wrapper': this.showSearchBox,
          'search-wrapper-hide': !this.showSearchBox,
          'is-mobile': this.$q.platform.is.mobile,
        }
      },
      locale() {
        return this.$i18n.locale
      },
      isEnUs() {
        return this.locale === appLang.enUs
      },
      mapOptions() {
        const options: { [key: string]: any } | maplibreGl.MapOptions = {
          zoom: this.mapZoom,
          center: [0, 0],
          minZoom: 1,
          maxZoom: 18,
          ...mapInfoCache,
        }
        // 系统内部限制最大为19级
        const maxZoom = Math.min(22, options.maxZoom)
        const minZoom = Math.max(3, options.minZoom)
        options.maxZoom = maxZoom
        options.minZoom = minZoom
        options.zoom =
          options.zoom < minZoom
            ? minZoom
            : options.zoom > maxZoom
              ? maxZoom
              : options.zoom

        return options
      },
      controllerData(): bysdb.IDbController[] {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Controller)
      },
      showKeyWordSearchResult() {
        return this.searchResult.length > 0 && this.isShowPanel
      },
      patrolReminderWithApp() {
        return this.$store.state.Settings.patrolReminder && isApp
      }
    },
    mounted(): void {
      this.init()

      StrPubSub.subscribe(BysMarkerDumpingAlarm, this.popupWindRecvBysMarkerSignal)
      StrPubSub.subscribe(WakeupBaiduMap, this.wakeBaiduMap)
      StrPubSub.subscribe(ShowTrackHistory, this.showTrackHistory)
      StrPubSub.subscribe(RemoveBysMarkerAlarm, this.removeAlarm)
      StrPubSub.subscribe(UpdateDbBysMarker, this.checkMarkerPopupPosition)
      StrPubSub.subscribe(UpdateDbController, this.checkControllerPopupPosition)
      StrPubSub.subscribe(CleanAllAlarmMarkerData, this.cleanAllAlarmMarkerData)

      // 订阅对界桩marker的状态处理事件
      StrPubSub.subscribe(MarkerHasSomeAbnormal, this.changeDeviceMarkerState)
    },
    beforeUnmount(): void {
      destroyLayerMarker()

      // LocalMap?.off()
      globalConfig.map = undefined

      StrPubSub.unsubscribe(BysMarkerDumpingAlarm, this.popupWindRecvBysMarkerSignal)
      StrPubSub.unsubscribe(WakeupBaiduMap, this.wakeBaiduMap)
      StrPubSub.unsubscribe(ShowTrackHistory, this.showTrackHistory)
      StrPubSub.unsubscribe(RemoveBysMarkerAlarm, this.removeAlarm)
      StrPubSub.unsubscribe(ToggleMapStyleFinish, this.renderMyLocationRedLineLayer)
      StrPubSub.unsubscribe(ToggleMapStyleFinish, this.initTrackHisIntoMapbox)
      StrPubSub.unsubscribe(UpdateDbBysMarker, this.checkMarkerPopupPosition)
      StrPubSub.unsubscribe(UpdateDbController, this.checkControllerPopupPosition)
      StrPubSub.unsubscribe(CleanAllAlarmMarkerData, this.cleanAllAlarmMarkerData)
      StrPubSub.unsubscribe(MarkerHasSomeAbnormal, this.changeDeviceMarkerState)
    },
  })
</script>

<style lang="scss">
  @import "quasar/src/css/variables";

  .q-show-android-dlg {
    width: 100vw !important;
    z-index: 7001 !important;
  }

  .q-show-pc-dlg {
    min-width: 250px !important;
    max-width: 300px !important;
    z-index: 7001 !important;
  }

  .q-show-dlg-cancel-label {
    border-top: 2px solid;
    border-color: #f3f3f3;
  }

  .full {
    width: 100%;
    height: 100%;
  }

  .maplibregl-map.mapbox-container {
    @extend .full;
    position: relative;

    .maplibregl-canvas-container {
      @extend .full;
      position: relative;
    }

    .maplibregl-control-container {
      .maplibregl-ctrl {
        &.custom-maplibregl-ctrl {
          .q-btn.custom-control-btn {
            border-radius: unset;
            display: flex;

            .q-btn__wrapper {
              padding: 0;
            }

            &.not-display {
              display: none;
            }

            &.geolocation.on-location {
              color: $light-blue-5;
            }

            &.geolocation.dis-location {
              color: #000000;
            }

            &.measure.active {
              color: $primary;
            }

            &.red-line.active {
              color: $negative;
            }
          }
        }

        .maplibregl-ctrl-logo {
          display: none !important;
        }

        &.maplibregl-ctrl-group button:focus {
          box-shadow: unset;
        }
      }

      .maplibregl-ctrl-bottom-left {
        .maplibregl-ctrl {
          .zoom-label {
            padding: 4px;
          }
        }
      }
    }

    .map-center-symbol {
      $symbol-color: #d50000;
      $symbol-width: 40px;

      position: absolute;
      top: 50%;
      left: 50%;
      margin-left: -20.5px;
      margin-top: -20.5px;
      z-index: 10;

      &:before,
      &:after {
        content: "";
        display: table;
        position: absolute;
        top: 0;
        left: 0;
      }

      &:before {
        border-bottom: 1px solid $symbol-color;
        width: $symbol-width;
        margin-top: 20.5px;
      }

      &:after {
        border-right: 1px solid $symbol-color;
        height: $symbol-width;
        margin-left: 20.5px;
      }
    }

    .maplibregl-popup-close-button {
      width: 30px;
      height: 30px;
      z-index: 10;
      font-size: 16px;
      outline: none;
    }
  }

  .distance-container {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    border-radius: 4px;
    padding: 4px 8px;

    &>.distance {
      color: #c10015;
      padding: 0 4px;
      font-size: 1.2rem;
    }
  }

  .maplibregl-popup {
    max-width: 100vw !important;
    min-width: 120px !important;
  }

  .search-wrapper {
    transition-duration: 0.3s;
    transition-timing-function: ease-in;
    width: 320px;

    &.is-mobile {
      width: calc(100vw - 60px);
    }

    .proposal-container .proposal-container-virtual-scroll {
      max-height: 60vh;
      overflow: auto;
    }

    .proposal-container .proposal-container-virtual-scroll-mobile {
      max-height: 75vh;
      overflow: auto;
    }

    .proposal-container {
      .proposal-item {
        padding: 2px 8px;

        .q-btn {
          display: inline-flex;
        }

        &.search-item-selected {
          background-color: rgba(25, 118, 210, 0.35) !important;
        }

        .q-item__section--avatar {
          min-width: unset;
          padding-right: 8px;
        }
      }
    }
  }

  .speed-controllers {
    min-width: 240px;
  }

  .search-wrapper-hide {
    transition-duration: 1s;
    transition-timing-function: ease-in;
    position: absolute;
    left: -300px;
    width: 320px;

    .proposal-container .proposal-container-virtual-scroll {
      max-height: 60vh;
      overflow: auto;
    }

    .proposal-container .proposal-container-virtual-scroll-mobile {
      max-height: 75vh;
      overflow: auto;
    }

    .proposal-container {
      .proposal-item {
        padding: 2px 8px;

        &.search-item-selected {
          background-color: rgba(25, 118, 210, 0.35) !important;
        }

        .q-item__section--avatar {
          min-width: unset;
          padding-right: 8px;

          .online {
            color: rgba(0, 255, 0, 1);
          }

          .uninstall {
            color: #ba68c8;
          }

          .uninstall-stone {
            color: gray;
          }
        }
      }
    }
  }

  .maplibregl-popup-content {
    padding-bottom: 10px;
  }

  .speed-controllers .q-btn-group .q-btn {
    border: none;
  }

  .track-his-popupWind {
    max-width: 350px;
  }

  .track-his-popupWind ul {
    list-style-type: none;
    padding: 0;
  }

  .alarm-marker-sticky {
    z-index: 999;
  }

  .alarm-marker-fab .q-btn__wrapper {
    padding: 8px;
    min-width: 32px;
    min-height: 32px;
  }

  .alarm-marker-badge {
    position: absolute;
    z-index: 1000;
    right: -6px;
    top: -6px;
  }

  .marker-installed {
    color: $orange;
  }

  .distance-draw {
    display: flex;

    .distance-result {
      color: $primary;
      margin: 0 8px;
    }
  }

  .mapbox-gl-draw_ctrl-draw-btn.draw-btn-active {
    background: unset !important;
    color: #00ff00;
    font-size: 16px;
  }

  .line-popup .maplibregl-popup-content .maplibregl-popup-close-button {
    width: 20px !important;
    height: 20px !important;
    padding: unset;
  }

  .maplibregl-ctrl-group {
    .q-btn>.q-btn__content {
      @apply h-full;
    }
  }
</style>
