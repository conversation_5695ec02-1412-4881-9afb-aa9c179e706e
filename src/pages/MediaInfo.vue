<template>
  <!-- visible,maximized in dataEdit mixins -->
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="dataLen ? '70vh' : 'auto'"
    modalClass="images-modal"
    @hide="hidePage"
  >
    <template v-slot:header><span>{{ title }}</span></template>
    <div
      class="row"
      style="max-height: 40px"
    >
      <div class="col-8 column ">
        <q-input
          outlined
          dense
          v-model="noFilter"
          :label="$t('common.search')"
          @update:model-value="HWIDFilter = ''"
          :debounce='350'
        >
          <template
            v-if="noFilter"
            v-slot:append
          >
            <q-icon
              name="cancel"
              @click.stop="noFilter = ''"
              class="cursor-pointer"
            />
          </template>
        </q-input>
      </div>
      <q-space />
      <q-btn
        dense
        class="q-px-sm"
        :disable="!canAdd"
        @click="selectNewImage"
      >{{ $t('common.upload') }}
      </q-btn>
    </div>
    <q-timeline
      color="secondary"
      class="q-ma-none q-mt-sm"
      style="height: calc(100% - 50px)"
    >
      <q-virtual-scroll
        class="full-height images-virtual-scroll"
        :items="data"
        separator
      >
        <template v-slot="{ item }">
          <q-timeline-entry :key='item.RID'>
            <template v-slot:title>
              <div class="row">
                <span class="text-h6">{{ getDeviceName(item) }}</span>
                <q-space />
                <q-btn
                  icon="create"
                  size="sm"
                  color="secondary"
                  round
                  :disable="!canEdit"
                  @click="editImage(item)"
                ></q-btn>
                <q-btn
                  icon="delete"
                  size="sm"
                  color="negative"
                  round
                  class="q-ml-sm"
                  :disable="!canDelete"
                  @click="deleteImage(item)"
                ></q-btn>
              </div>
            </template>

            <template v-slot:subtitle>
              <div> {{ formatUploadTime(item.UploadTime) }}</div>
            </template>

            <template v-if="item.MediaType === 1">
              <bys-image
                :img-url="formatMediaUrl(item.MediaOrigFileName)"
                :nodes="formatPannellumInfo(item)"
              />
            </template>
            <template v-else-if="item.MediaType === 2">
              <pannellum
                v-if="item.MediaOrigFileName"
                :imgUrl="formatMediaUrl(item.MediaOrigFileName)"
                :nodes="formatPannellumInfo(item)"
                :id="'panorama-' + item.RID"
                class="image-editor-panorama-view"
              />
            </template>
            <template v-else-if="item.MediaType === 3">
              <video-player
                v-if="item.MediaOrigFileName"
                :source="formatMediaUrl(item.MediaOrigFileName)"
                :nodes="formatPannellumInfo(item)"
                :id="'video-player-' + item.RID"
              />
            </template>
            <template v-else></template>
          </q-timeline-entry>
        </template>
      </q-virtual-scroll>
    </q-timeline>

    <!-- 上传图片等数据对话框 -->
    <q-dialog
      v-model="formVisible"
      persistent
      transition-show="slide-up"
      transition-hide="slide-down"
      ref="form"
      :class="{ 'image-editor': true, 'maximized': $q.screen.lt.sm }"
      @hide="hideFormDialog"
    >
      <q-card class="image-editor-card">
        <q-bar class="bg-primary text-white">
          <q-toolbar-title>
            <h6 class="modal-header-title q-ma-none">
              <span>{{ `${title ? title + ' / ' : ''}${$t('common.upload')}` }}</span>
            </h6>
          </q-toolbar-title>
          <q-btn
            flat
            v-close-popup
            round
            dense
            icon="close"
          />
        </q-bar>

        <q-card-section class="q-pa-none">
          <q-stepper
            class="full-width image-editor-stepper q-pa-none"
            v-model="uploadStep"
            color="primary"
            vertical
            animated
            flat
            keep-alive
            :swipeable="$q.screen.lt.sm"
          >
            <q-step
              :name="1"
              :title="$t('form.selectImages')"
              icon="photo"
              :done="uploadStep > 1"
            >
              <q-uploader
                class="image-uploader"
                :factory="uploaderFactory"
                multiple
                flat
                bordered
                :accept="accept[acceptKey]"
                :max-file-size="100 * 1024 * 1024"
                ref="imageUploader"
                @added="addFiles"
                @removed="removedFiles"
                @uploaded="uploaded"
                @failed="failed"
                @finish="uploadFinish"
              >
                <template v-slot:header="scope">
                  <div class="row no-wrap items-center q-pa-sm q-gutter-xs">
                    <q-btn
                      v-if="scope.queuedFiles.length > 0"
                      icon="clear_all"
                      @click="scope.removeQueuedFiles"
                      round
                      dense
                      flat
                    >
                    </q-btn>
                    <div class="col">
                      <div class="q-uploader__title">{{ scope.queuedFiles.length }} / {{ scope.uploadSizeLabel }}</div>
                    </div>
                    <q-btn
                      v-if="isApp"
                      icon="add_a_photo"
                      round
                      dense
                      flat
                      @click="addPhoto"
                    />
                    <q-btn
                      v-if="scope.canAddFiles"
                      icon="add_box"
                      round
                      dense
                      flat
                    >
                      <q-uploader-add-trigger />
                    </q-btn>
                  </div>
                </template>
              </q-uploader>

              <q-stepper-navigation class="flex justify-end items-end">
                <div class="uploadTips">{{ $t('common.uploadTips') }}</div>
                <q-btn
                  @click="nextStep"
                  color="primary"
                  :label="$t('common.continue')"
                  class="q-ml-sm"
                  :disable="disUploadContinue"
                />
              </q-stepper-navigation>
            </q-step>

            <q-step
              :name="2"
              :title="$t('form.deviceContext')"
              icon="photo"
            >
              <q-form
                @submit="stepFinish"
                ref="form"
              >
                <div class="col col-xs-12 q-gutter-sm q-col-gutter-sm">
                  <q-option-group
                    v-model="currentRow.MediaType"
                    :options="MediaTypeOptions"
                    color="primary"
                    inline
                    dense
                  />
                </div>
                <div class="col col-xs-12 q-gutter-sm q-col-gutter-sm">
                  <q-select
                    v-model="currentRow.MarkerRID"
                    :label="$t('menus.boundaryMarker')"
                    outlined
                    dense
                    lazy-rules
                    :rules="rules.MarkerRID"
                    :options="bysMarkerOptions"
                    @filter="filterBysMarker"
                    options-dense
                    map-options
                    emit-value
                    use-input
                    :disable="isController || HWIDFilter !== ''"
                    ref="MarkerRID"
                  >
                    <template v-slot:before>
                      <q-checkbox
                        v-model="isBysMarker"
                        dense
                        @update:model-value="isBysMarkerInput"
                        :disable="HWIDFilter !== ''"
                      />
                    </template>
                  </q-select>
                </div>
                <div class="col col-xs-12 q-gutter-sm q-col-gutter-sm">
                  <q-select
                    v-model="currentRow.ControllerRID"
                    :label="$t('menus.controller')"
                    outlined
                    dense
                    lazy-rules
                    :rules="rules.ControllerRID"
                    :options="controllerOptions"
                    @filter="filterController"
                    options-dense
                    map-options
                    emit-value
                    use-input
                    :disable="isBysMarker || HWIDFilter !== ''"
                    ref="ControllerRID"
                  >
                    <template v-slot:before>
                      <q-checkbox
                        v-model="isController"
                        dense
                        @update:model-value="isControllerInput"
                        :disable="HWIDFilter !== ''"
                      />
                    </template>
                  </q-select>
                </div>
                <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
                  <q-input
                    v-model="currentRow.MediaDescription"
                    type="textarea"
                    :label="$t('form.description')"
                    outlined
                    dense
                    clearable
                    autogrow
                    :maxlength="128"
                  />
                </div>

                <q-stepper-navigation align="right">
                  <q-btn
                    @click="backStep"
                    flat
                    color="primary"
                    :label="$t('common.back')"
                    :disable="isUploading"
                  />
                  <q-btn
                    type="submit"
                    color="primary"
                    :label="$t('common.finish')"
                    class="q-ml-sm"
                    :loading="isUploading"
                  >
                    <template v-slot:loading>
                      <q-spinner class="q-uploader__spinner" />
                    </template>
                  </q-btn>
                </q-stepper-navigation>
              </q-form>
            </q-step>
          </q-stepper>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 编辑图片等数据对话框，只能修改关联的数据，不能修改图片 -->
    <q-dialog
      v-model="editVisible"
      persistent
      transition-show="slide-up"
      transition-hide="slide-down"
      ref="form"
      :class="{ 'image-editor': true, 'maximized': $q.screen.lt.sm }"
      @hide="hideEditorDialog"
    >
      <q-card class="image-editor-card">
        <q-bar class="bg-primary text-white">
          <q-toolbar-title>
            <h6 class="modal-header-title q-ma-none">
              <span>{{ `${title ? title + ' / ' : ''}${$t('common.upload')}` }}</span>
            </h6>
          </q-toolbar-title>
          <q-btn
            flat
            v-close-popup
            round
            dense
            icon="close"
          />
        </q-bar>

        <q-card-section>
          <q-form @submit="confirmForm">
            <div class="col col-xs-12 q-gutter-sm q-col-gutter-sm">
              <q-select
                v-model="currentRow.MarkerRID"
                :label="$t('menus.boundaryMarker')"
                outlined
                dense
                lazy-rules
                :rules="rules.MarkerRID"
                :options="bysMarkerOptions"
                @filter="filterBysMarker"
                options-dense
                map-options
                emit-value
                use-input
                :disable="isController"
                ref="MarkerRID"
              >
                <template v-slot:before>
                  <q-checkbox
                    v-model="isBysMarker"
                    dense
                    @update:model-value="isBysMarkerInput"
                  />
                </template>
              </q-select>
            </div>
            <div class="col col-xs-12 q-gutter-sm q-col-gutter-sm">
              <q-select
                v-model="currentRow.ControllerRID"
                :label="$t('menus.controller')"
                outlined
                dense
                lazy-rules
                :rules="rules.ControllerRID"
                :options="controllerOptions"
                @filter="filterController"
                options-dense
                map-options
                emit-value
                use-input
                :disable="isBysMarker"
                ref="ControllerRID"
              >
                <template v-slot:before>
                  <q-checkbox
                    v-model="isController"
                    dense
                    @update:model-value="isControllerInput"
                  />
                </template>
              </q-select>
            </div>
            <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
              <q-input
                v-model="currentRow.MediaDescription"
                type="textarea"
                :label="$t('form.description')"
                outlined
                dense
                clearable
                autogrow
                :maxlength="128"
              />
            </div>

            <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm text-center q-mt-sm">
              <q-btn
                type="submit"
                color="primary"
                :label="$t('common.confirm')"
                class="q-ml-sm w-32"
                :loading="isLoading"
                :disable="isLoading"
              >
                <template v-slot:loading>
                  <q-spinner class="q-uploader__spinner" />
                </template>
              </q-btn>
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>
  </modal>
</template>


<script lang="ts">
  import { defineAsyncComponent, defineComponent, h } from 'vue'
  import dialogMixin from '@src/utils/mixins/dialog'
  import dataEditMixin from '@src/utils/mixins/editor'
  import { toLocalTime, utcTime } from '@src/utils/dayjs'
  import { v1 as uuidV1 } from 'uuid'
  import { ICallOption } from 'jsyrpc'
  import { yrpcmsg } from 'yrpcmsg'
  import { crud } from '@ygen/crud'
  import log from '@src/utils/log'
  import { DataName } from '@src/store/data'
  import { GET_DATA, NS } from '@src/store/data/methodTypes'
  import { StrPubSub } from 'ypubsub'
  import dataStore, { BysMarker, Controller, MediaInfo, unknownUserData } from '@services/dataStore'
  import { cloneDeep } from 'lodash'
  import { DbName, sortData } from '@src/utils/permission'
  import { dataEditStatus, outputDBError } from '@src/utils/common'
  import { bysdb } from '@ygen/bysdb'
  import { required } from '@utils/validation'
  import { PrpcDbMediaInfo } from '@ygen/bysdb.rpc.yrpc'
  import { user } from '@ygen/user'
  import { DeleteMediaInfo, InsertMediaInfo, UpdateMediaInfo } from '@utils/pubSubSubject'
  import { useBysMarkerData } from '@utils/vueUse/bysMarkerData'
  import Pannellum from '@components/pannellum/Pannellum.vue'
  import BysImage from '@components/bysImage/BysImage.vue'
  import { i18n } from '@boot/i18n'
  import VideoPlayer from '@components/videoPlayer/VideoPlayer.vue'
  import { isApp } from '@src/config'
  import '@utils/cordova-patch/fileReader'
  import { resolveServerPath } from '@utils/path'
  import { safeCamera } from '@utils/capacitorUtils'

  const imageError = {
    dbmediainfo_controllerrid_fkey: i18n.global.t('dataBaseError.controllerNotExist'),
    dbmediainfo_bysmarker_fkey: i18n.global.t('dataBaseError.markerNotExist'),
    dbmediainfo_orgrid_fkey: i18n.global.t('dataBaseError.unitNotExist'),
    dbmediainfo_uploaduserrid_fkey: i18n.global.t('dataBaseError.uploadUserNotExist'),
    'No operation permission': i18n.global.t('dataBaseError.permissionErr'),
  }
  const deleteWarning = defineAsyncComponent(() => import('@components/deleteWarning/DeleteWarning.vue'))
  const { bysMarkerData } = useBysMarkerData()

  export default defineComponent({
    name: 'Images',
    mixins: [dialogMixin, dataEditMixin],
    data() {
      return {
        HWIDFilter: '',
        HWIDIsMarker: true,
        noFilter: '',
        selectedImage: null,
        formVisible: false,
        editStatus: dataEditStatus.show,
        isLoading: false,
        isBysMarker: true,
        isController: false,
        bysMarkerFilter: '',
        controllerFilter: '',
        uploadStep: 1,
        currentRow: {
          MediaDescription: '',
          MediaType: 1,
          MarkerRID: '',
          ControllerRID: '',
        } as bysdb.IDbMediaInfo,
        mediaInfoList: [],
        hasFailed: false,
        isUploading: false,
        files: [] as File[],
        editVisible: false,
        // accept: 'video/mp4,video/x-m4v,video/*,audio/*,image/*',
        accept: {
          default: 'video/mp4,video/x-m4v,video/*,audio/*,image/*',
          image: 'image/*',
          video: 'video/mp4,video/x-m4v,video/*,audio/*'
        },
        acceptKey: 'default'
      }
    },
    computed: {
      bysMarkerData() {
        return bysMarkerData.value
      },
      dbName() {
        return DbName.DbMediaInfo
      },
      isApp() {
        return isApp
      },
      cameraOptions() {
        // https://github.com/apache/cordova-plugin-camera#module_camera.getPicture
        return {
          quality: 30,
          destinationType: Camera.DestinationType.DATA_URL,
          sourceType: Camera.PictureSourceType.CAMERA,
          encodingType: Camera.EncodingType.JPEG,
          mediaType: Camera.MediaType.ALLMEDIA,
          correctOrientation: true,
        }
      },
      title() {
        return this.$t('menus.images')
      },
      dataLen() {
        return this.data.length
      },
      data() {
        const allData = this.$store.getters[`${NS}/${GET_DATA}`](DataName.MediaInfo)
        let dataList
        if (this.HWIDFilter !== '') {
          //根据HWID筛选
          let target
          if (this.HWIDIsMarker) {
            target = this.bysMarkerData.find(item => item.MarkerHWID === this.HWIDFilter)
            dataList = allData.filter(item => item.MarkerRID === target?.RID)
          } else {
            target = this.controllerData.find(item => item.ControllerHWID === this.HWIDFilter)
            dataList = allData.filter(item => item.ControllerRID === target?.RID)
          }
        } else {
          //根据编号筛选
          if (this.noFilter !== '') {
            dataList = allData.filter(
              item => {
                let filterString = ''
                let target: bysdb.IDbBysMarker | bysdb.IDbController | undefined
                // not found marker
                target = BysMarker.getData(item.MarkerRID) as bysdb.IDbBysMarker | undefined
                if (target) {
                  const marker = target as bysdb.IDbBysMarker
                  filterString = '' + marker.MarkerNo + marker.MarkerDescription
                } else {
                  target = Controller.getData(item.ControllerRID) as bysdb.IDbController | undefined

                  if (target) {
                    const controller = target as bysdb.IDbController
                    filterString = '' + controller.ControllerNo + controller.ControllerDescription
                  } else {
                    return false
                  }
                }
                return filterString.includes(this.noFilter)
              },
            )
          } else {
            dataList = allData
          }
        }
        // 按时间进行降序排序，最后添加的，显示最前
        return sortData(dataList, {
          descending: true,
          prop: 'UploadTime',
        })
      },
      bysMarkerOptions() {
        return this.bysMarkerData
          .map((data: bysdb.IDbBysMarker) => {
            return { label: data.MarkerNo, value: data.RID }
          })
          .filter(option => {
            const needle = this.bysMarkerFilter.toLowerCase()
            return option.label.toLowerCase().includes(needle)
          })
      },
      controllerData(): bysdb.IDbController[] {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Controller)
      },
      controllerOptions() {
        return this.controllerData
          .map((data: bysdb.IDbController) => {
            return { label: data.ControllerNo, value: data.RID }
          })
          .filter(option => {
            const needle = this.controllerFilter.toLowerCase()
            return option.label.toLowerCase().includes(needle)
          })
      },
      dataEditStatus() {
        return dataEditStatus
      },
      rules() {
        if (this.isController) {
          return {
            MarkerRID: [],
            ControllerRID: [
              val => required(val) || this.$t('rules.required'),
            ],
          }
        }
        if (this.isBysMarker) {
          return {
            ControllerRID: [],
            MarkerRID: [
              val => required(val) || this.$t('rules.required'),
            ],
          }
        }

        return {
          ControllerRID: [],
          MarkerRID: [],
        }
      },
      MediaTypeOptions() {
        return [
          { label: this.$t('form.normalImage'), value: 1 },
          { label: this.$t('form.panorama'), value: 2 },
          { label: this.$t('form.video'), value: 3 },
        ]
      },
      disUploadContinue() {
        return this.files.length === 0
      },
    },
    methods: {
      clearFormData() {
        if (!this.HWIDFilter) {
          this.isBysMarker = true
          this.isController = false
          this.currentRow.MarkerRID = this.currentRow.ControllerRID = ''
        }
        this.isUploading = false
        this.uploadStep = 1
        //清空预览的图片
        this.files = []
        this.mediaInfoList = []
        this.currentRow.MediaDescription = ''
        this.$refs.imageUploader?.reset()
      },
      hidePage() {
        //置空筛选条件
        this.HWIDFilter = this.noFilter = ''
        this.clearFormData()
      },
      dataURItoBlob(dataURI) {
        let mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0] // mime类型
        let byteString = atob(dataURI.split(',')[1]) //base64 解码
        let arrayBuffer = new ArrayBuffer(byteString.length) //创建缓冲数组
        let intArray = new Uint8Array(arrayBuffer) //创建视图

        for (let i = 0; i < byteString.length; i++) {
          intArray[i] = byteString.charCodeAt(i)
        }
        return new Blob([intArray], { type: mimeString })
      },
      cameraSuccess(dataUri) {
        const blob = this.dataURItoBlob(dataUri)
        blob.lastModified = Date.now()
        blob.lastModifiedDate = new Date()
        blob.name = Date.now() + '.jpeg'
        this.$refs.imageUploader?.addFiles([blob])
      },
      cameraError(err: string) {
        // 没有选择图片，不需要消息提示
        if (err.includes('No Image Selected') || err.includes('User cancelled')) {
          return
        }

        this.$q.notify({
          message: this.$t('message.cameraError') as string,
          type: 'warning',
        })
      },
      async addPhoto() {
        try {
          // 安全地使用 Capacitor Camera
          const cameraModule = await safeCamera()
          if (!cameraModule) {
            return
          }

          const { Camera, CameraResultType, CameraSource } = cameraModule
          const image = await Camera.getPhoto({
            quality: 30,
            resultType: CameraResultType.DataUrl,
            source: CameraSource.Camera,
            correctOrientation: true
          })
          // image.dataUrl 是带前缀的 base64
          this.cameraSuccess(image.dataUrl)
        } catch (err) {
          this.cameraError(err && err.message ? err.message : String(err))
        }
      },
      formatPannellumInfo(data: bysdb.IDbMediaInfo) {
        return {
          Author: this.getUploadUser(data.UploadUserRID as string),
          UploadTime: this.formatUploadTime(data.UploadTime as string),
          LastUploadUser: data.UploadUserRID === data.LastUpdateUserRID ? undefined : this.getUploadUser(
            data.LastUpdateUserRID as string),
          LastUploadTime: data.UploadTime === data.LastUpdateTime ? undefined : this.formatUploadTime(
            data.LastUpdateTime as string),
          Description: data.MediaDescription,
        }
      },
      formatMediaInfo(data: bysdb.IDbMediaInfo) {
        // const h = this.$createElement
        const createInfoItem = (option) => {
          return h('div', {
            class: {
              'image-info-item': true,
            },
          }, [
            h('span', {
              class: {
                'image-info-item--label': true,
                'q-mr-sm': true,
              },
            }, option.label || ''),
            h('span', {
              class: {
                'image-info-item--value': true,
              },
            }, option.value || ''),
          ])
        }
        const infoList = [
          { label: this.$t('form.uploadUser'), value: this.getUploadUser(data.UploadUserRID as string) },
          { label: this.$t('form.uploadTime'), value: this.formatUploadTime(data.UploadTime as string) },
          { label: this.$t('form.description'), value: data.MediaDescription },
        ]

        return h('div', {
          class: {
            'absolute-top': true,
            'text-subtitle2': true,
            'image-info': true,
          },
        }, infoList.map(item => createInfoItem(item)))
      },
      getUploadUser(UploadUserRID: string) {
        const allUserData: user.IDbUser[] = dataStore.User.getDataList().concat(unknownUserData)
        const userData = allUserData.find(item => item.RID === UploadUserRID)
        return userData?.UserName || ''
      },
      getDeviceName(data: bysdb.IDbMediaInfo) {
        // 关联的设备界桩
        if (data.MarkerRID) {
          return (BysMarker.getData(data.MarkerRID) as bysdb.IDbBysMarker | undefined)?.MarkerNo || ''
        }

        // 关联的设备为控制器
        return (Controller.getData(data.ControllerRID as string) as bysdb.IDbController | undefined)?.ControllerNo || ''
      },
      formatUploadTime(UploadTime: string) {
        // UploadTime为utc时间，需要转换
        return toLocalTime(UploadTime)
      },
      formatMediaUrl(MediaOrigFileName: string) {
        return resolveServerPath(`/media/${MediaOrigFileName}`)
      },
      addFiles(files: File[]) {
        if (this.files.length === 0) {
          this.acceptKey = files[0].type.startsWith('image/') ? 'image' : 'video'
          this.currentRow.MediaType = files[0].type.startsWith('image/') ? 1 : 3
        }
        this.files = files
      },
      removedFiles(files: File[]) {
        this.files = this.files.filter(file => !files.includes(file))
        if (this.$refs.imageUploader.files.length === 0) {
          this.acceptKey = 'default'
          this.currentRow.MediaType = 1
        }
      },
      uploadFinish() {
        // 无论上传成功或失败，均会调用
        this.hideFormDialog()
        if (!this.hasFailed) {
          this.hasFailed = false
          // 全部上传成功，则提示
          this.$q.notify({
            message: this.$t('message.uploadSuccess') as string,
            type: 'positive',
            color: 'positive',
            position: 'top',
          })
        }
      },
      uploaded(info: { [key: string]: any }) {
        const mediaInfo: bysdb.IDbMediaInfo = this.mediaInfoList.find(v => v.RID === info.files[0].$RID)
        if (!mediaInfo) {
          return
        }
        this.insertData(mediaInfo).catch(() => {
          const info = {
            files: [{ name: mediaInfo.MediaOrigFileName }],
          }
          this.failed(info)
          //终止上传
          this.$refs.imageUploader?.abort()
        })
      },
      failed(info: { [key: string]: any }) {
        log.error('failed', info)
        this.hasFailed = true
        // 提示失败
        this.$q.notify({
          message: this.$t('message.uploadFailed') + ' ' + info.files[0].name,
          type: 'negative',
          color: 'negative',
          position: 'top',
        })
      },
      uploaderFactory(files: Array<File & { [key: string]: any }>) {
        const file = files[0]
        // 生成IDbMediaInfo对象，合并关联数据
        const mediaInfo: bysdb.IDbMediaInfo = {}
        const tempMediaInfo = this.getDefFormData()
        Object.assign(mediaInfo, this.currentRow, tempMediaInfo)

        const targetData = this.isBysMarker ? BysMarker.getData(this.currentRow.MarkerRID) : Controller.getData(
          this.currentRow.ControllerRID)
        mediaInfo.OrgRID = targetData?.OrgRID || this.$store.state.UserOrgRID

        // 处理文件名，格式为uuid.xxx，详见 https://git.cchdx.com/bys/server/-/issues/21
        mediaInfo.MediaOrigFileName = `${mediaInfo.RID}.${file.name.split('.').pop()}`
        mediaInfo.LastUpdateTime = utcTime()
        mediaInfo.LastUpdateUserRID = this.$store.state.UserRID
        this.mediaInfoList.push(mediaInfo)

        // 缓存对应数据库表的RID，以便在上传成功时进行表插入操作
        file.$RID = mediaInfo.RID

        const url = resolveServerPath('/mediaUpload')

        return {
          url,
          formFields: [
            { name: 'file', value: file },
            { name: 'mediaFileName', value: mediaInfo.MediaOrigFileName },
            { name: 'sid', value: this.$store.state.SessionRID },
            { name: 'sys', value: this.$store.state.System },
          ],
          headers: [
            { name: 'Access-Control-Allow-Origin', value: '*' },
            { name: 'Access-Control-Allow-Methods', value: 'GET, POST, PUT, DELETE, PATCH, OPTIONS' },
            {
              name: 'Access-Control-Allow-Headers',
              value: 'Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization',
            },
          ],
        }
      },
      nextStep() {
        this.uploadStep++
      },
      backStep() {
        this.uploadStep--
      },
      stepFinish() {
        this.$refs.form.validate()
          .then(() => {
            this.isUploading = true
            this.$refs.imageUploader?.upload()
          })
      },
      // 图片关联的设备过滤器方法
      filterBysMarker(val, update) {
        this.bysMarkerFilter = val
        update()
      },
      filterController(val, update) {
        this.controllerFilter = val
        update()
      },
      // 关联控制器、界桩的互斥关系，二选一
      isBysMarkerInput(val) {
        this.isController = !val
        if (val) {
          this.currentRow.ControllerRID = null
          this.$refs.ControllerRID?.resetValidation()
        } else {
          this.currentRow.MarkerRID = null
          this.$refs.MarkerRID?.resetValidation()
        }
      },
      isControllerInput(val) {
        this.isBysMarker = !val
        if (val) {
          this.currentRow.MarkerRID = null
          this.$refs.MarkerRID?.resetValidation()
        } else {
          this.currentRow.ControllerRID = null
          this.$refs.ControllerRID?.resetValidation()
        }
      },
      // 选择图片、视频等资源文件
      selectNewImage() {
        this.formVisible = true
        this.editStatus = dataEditStatus.add
      },
      hideFormDialog() {
        this.clearFormData()
        this.formVisible = false
        this.editStatus = dataEditStatus.show
        this.acceptKey = 'default'
      },
      // 新建数据的默认方法
      getDefFormData() {
        const data: bysdb.IDbMediaInfo = {
          RID: uuidV1(),
          UploadUserRID: this.$store.state.UserRID,
          UploadTime: utcTime(),
          Setting: '{}',
        }
        return data
      },
      // 数据库操作方法
      insertData(data: bysdb.IDbMediaInfo): Promise<boolean> {
        return new Promise((resolve, reject) => {
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbMediaInfo Insert result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject('failed')
              } else {
                resolve(true)
                // 同步到数据容器对象
                const RID = data.RID as string
                MediaInfo.setData(RID, data)
                StrPubSub.publish(InsertMediaInfo, data)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              //"/ERROR: duplicate key value violates unique constraint "dbmediainfo_pkey" (SQLSTATE 23505)"
              log.error('IDbMediaInfo Insert server error', errRpc)
              // 处理服务器响应的错误
              let reason = outputDBError(imageError, errRpc.Optstr)
              // // dborg_orgid_key 自编号重复
              // if (errRpc.Optstr.includes('dborg_orgid_key')) {
              //   reason = this.$t('message.duplicateOrgID', { name: data.FileName })
              // }
              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbMediaInfo Insert local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbMediaInfo Insert timeout', v)
              const options = {
                action: this.$t('common.add'),
                name: data.MediaOrigFileName,
              }
              const reason = this.$t('message.timeout', options)
              reject(reason)
            },
          }
          PrpcDbMediaInfo.Insert(data, options)
        })
      },
      updateData(data: bysdb.IDbMediaInfo): Promise<boolean> {
        return new Promise((resolve, reject) => {
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbMediaInfo Update result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject('failed')
              } else {
                resolve(true)
                // 同步到数据容器对象
                const RID = data.RID as string
                const oldData = cloneDeep(MediaInfo.getData(RID))
                MediaInfo.setData(RID, new bysdb.DbMediaInfo(data))
                StrPubSub.publish(UpdateMediaInfo, data, oldData)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbMediaInfo Update server error', errRpc)
              // 处理服务器响应的错误
              let reason = outputDBError(imageError, errRpc.Optstr)
              // // dborg_orgid_key 自编号重复
              // if (errRpc.Optstr.includes('dborg_orgid_key')) {
              //   reason = this.$t('message.duplicateOrgID', { name: data.FileName })
              // }
              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbMediaInfo Update local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbMediaInfo Update timeout', v)
              const reason = 'timeout'
              reject(reason)
            },
          }
          PrpcDbMediaInfo.Update(data, options)
        })
      },
      deleteData(data: bysdb.IDbMediaInfo): Promise<boolean> {
        return new Promise((resolve, reject) => {
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbMediaInfo Delete result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject('failed')
              } else {
                resolve(true)
                // 同步到数据容器对象
                const key = data.RID as string
                MediaInfo.deleteData(key)
                StrPubSub.publish(DeleteMediaInfo, data)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbMediaInfo Delete server error', errRpc)
              // 处理服务器响应的错误
              let reason = outputDBError(imageError, errRpc.Optstr)
              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbMediaInfo Delete local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbMediaInfo Delete timeout', v)
              const reason = 'timeout'
              reject(reason)
            },
          }
          PrpcDbMediaInfo.Delete(data, options)
        })
      },
      // 删除一个图片
      cancelDelete() {
        // 释放空间
        this.currentRow = {}
      },
      confirmDelete(data: bysdb.IDbMediaInfo) {
        this.deleteData(data)
          .catch((reason: string) => {
            this.$q.notify({
              message: reason,
              type: 'warning',
              color: 'warning',
              position: 'top',
            })
          })
      },
      deleteImage(data: bysdb.IDbMediaInfo) {
        // this.currentRow = { ...data }
        this.$q.dialog({
          component: deleteWarning,
          componentProps: {
            // content: this.delWarnContent,
          },
        }).onOk(() => {
          this.confirmDelete(data)
        // }).onCancel(() => {
        })
      },
      // 编辑修改图片
      hideEditorDialog() {
        this.editVisible = false
        this.editStatus = dataEditStatus.show
      },
      editImage(data: bysdb.IDbMediaInfo) {
        this.editVisible = true
        this.editStatus = dataEditStatus.edit
        this.currentRow = { MarkerRID: '', ControllerRID: '', ...data }
        if (data.ControllerRID) {
          this.isControllerInput(this.isController = true)
          this.isBysMarkerInput(this.isBysMarker = false)
        } else {
          this.isControllerInput(this.isController = false)
          this.isBysMarkerInput(this.isBysMarker = true)
        }
      },
      confirmForm() {
        this.isLoading = true
        this.currentRow.LastUpdateTime = utcTime()
        this.currentRow.LastUpdateUserRID = this.$store.state.UserRID
        this.updateData(this.currentRow)
          .then(this.hideEditorDialog)
          .finally(() => {
            this.isLoading = false
          })
      },
    },
    components: {
      Pannellum, BysImage, VideoPlayer,
    },
  })
</script>

<style lang="scss">
  .image-preview-card {
    width: 24vw;
    height: auto;

    &.maximized {
      width: 90vw;
    }
  }

  .image-editor {
    .q-card {
      min-width: 35vw;
    }

    &.maximized {
      .q-card {
        min-width: unset;
        width: 100vw;
      }
    }
  }

  .image-editor-stepper {
    .image-uploader {
      width: 100%;
    }

    .uploadTips {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      width: 100px;
      font-size: 12px;
      color: $grey;
    }
  }

  .images-virtual-scroll {
    .q-virtual-scroll__content {
      padding-right: 10px;
    }

    .image-editor-panorama-view {
      position: relative;
      width: 100%;
      height: 200px;

      &.maximized {
        width: 100%;
        height: 200px;
      }

      .image-info {
        color: #fff;
        background: rgba(0, 0, 0, 0.47);
      }
    }

    .images {
      min-height: 120px;
    }

    .image-info {
      padding: 4px 6px;
      z-index: 1;

      .image-info-item {
        font-size: 12px;
        line-height: 20px;
      }
    }

    .video-container {
      position: relative;
      min-height: 150px;

      .image-info {
        color: #fff;
        background: rgba(0, 0, 0, 0.47);
      }
    }
  }

  .images-modal .bys-move-layout.q-card:not(.maximized) {
    width: 60vw;
    max-width: unset !important;
  }

  .q-dialog.fullscreen.image-editor {
    z-index: 7000 !important;
  }
</style>
