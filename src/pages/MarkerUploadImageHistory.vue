<template>
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="maxHeight ? '60vh' : 'auto'"
    content-class="marker-history-modal"
    @hide="maxHeight = false"
  >
    <template #header>
      <span v-text="title"></span>
    </template>
    <query-history
      ref="form-content"
      :default-time-diff="defaultTimeDiff"
      :time-max-range="timeMaxRange"
      :export-name="title"
      :table-columns="columns"
      :filter-columns='filterColumns'
      :time-limit-sql-name="timeField"
      :can-query="canQuery"
      :extraExportFunc="extraExportFunc"
      @submit-clicked="onSubmit"
      @query-canceled="queryCanceled"
      @update:pagination="updatePagination"
    >
      <template #form>
        <div class="query-form">
          <q-select
            v-model="orgRID"
            :label="$t('form.parentUnit')"
            outlined
            dense
            clearable
            lazy-rules
            :rules="[]"
            :options="orgRIDOptions"
            @filter="filterOrgRID"
            options-dense
            map-options
            emit-value
            @update:model-value="orgRIDChange"
          />
          <q-select
            v-model="markerRID"
            :label="$t('form.markerName')"
            outlined
            dense
            clearable
            lazy-rules
            :rules="[]"
            :options="markerRIDOptions"
            @filter="filterMarkerRID"
            options-dense
            map-options
            emit-value
            use-input
          >
            <template v-slot:before>
              <q-checkbox v-model='extractSearchMarker'>
                <q-tooltip>
                  {{ $t('common.exactSearch') }}
                </q-tooltip>
              </q-checkbox>
            </template>
          </q-select>
        </div>
      </template>

      <template v-slot:my-body-cell-Image="{ value,/* row, col */}">
        <q-img
          v-if="!!value"
          :src="value"
          :ratio="16/9"
          spinner-color="primary"
          spinner-size="2rem"
          fit="contain"
          class="cursor-pointer min-w-8 max-w-16"
          loading="lazy"
          @click.stop.prevent="previewImageWithLightbox(value)"
        >
          <q-tooltip>
            {{ $t('historyTable.clickToViewFullImage') }}
          </q-tooltip>
        </q-img>
      </template>

      <template v-slot:my-body-cell-Action="{ row }">
        <q-btn
          icon="download"
          color="primary"
          flat
          dense
          @click.stop="downloadCameraImage(row)"
        >
          <q-tooltip>
            {{ $t('common.download') }}
          </q-tooltip>
        </q-btn>
      </template>
    </query-history>

    <teleport to="body">
      <lightbox
        class="upload-image-preview-lightbox"
        :visible="visibleLightbox"
        :imgs="allImageUrls"
        :index="lightboxIndex"
        :maskClosable="false"
        @hide="onHideLightbox"
      >
      </lightbox>
    </teleport>
  </modal>
</template>

<script lang="ts">
  import dialogMixin from '@utils/mixins/dialog'
  import historyMixin from '@utils/mixins/history'
  import { org } from '@ygen/org'
  import { bysdb } from '@ygen/bysdb'
  import { BysMarker, Unit } from '@services/dataStore'
  import { deferred, getObjAllAttrs, IControllerOptions, MarkerType, } from '@utils/common'
  import { crud } from '@ygen/crud'
  import { defaultQueryFinished, QueryBatchV2 } from '@services/queryData'
  import { PrpcDbMarkerUploadImageHistory } from '@ygen/bysdb.rpc.yrpc'
  import { StrPubSub } from 'ypubsub'
  import { QueryMarkerUploadImageHistory } from '@utils/pubSubSubject'
  import { DbName } from '@utils/permission'
  import PermissionMixin from '@utils/mixins/permission'
  import { defineComponent, shallowRef } from 'vue'
  import { useBysMarkerData } from '@utils/vueUse/bysMarkerData'
  import { resolveServerPath } from '@utils/path'
  import log from 'loglevel'
  import Lightbox from 'vue-easy-lightbox'
  import { type Img } from 'vue-easy-lightbox/types/types'
  import { i18n } from '@boot/i18n'

  const { bysMarkerData } = useBysMarkerData()
  const queryRet = shallowRef<bysdb.IDbMarkerUploadImageHistory[]>([])
  let queryNormal = true
  let queryCancelHandlers: Array<() => void> = []
  // 保存上传图片文件blob url
  // 1cfb4676-5af1-47ed-b167-a39a4f08c23d.jpg -> 图片的Blob资源
  const UploadImageFileContents = new Map<string, Blob>()

  export default defineComponent({
    name: 'MarkerUploadImageHistory',
    components: { Lightbox },
    mixins: [dialogMixin, PermissionMixin, historyMixin],
    data() {
      return {
        extractSearchMarker: false,

        maxHeight: false,
        defaultTimeDiff: {
          value: 1,
          uint: 'month',
        },
        timeMaxRange: {
          value: 3,
          uint: 'month',
          text: this.$t('date.notMax3month'),
        },
        timeField: 'UploadTime',

        orgRID: '',
        markerRID: '',

        orgFilter: '',
        controllerFilter: '',
        deviceFilter: '',

        // 图片预览显示控制
        visibleLightbox: false,
        lightboxIndex: 0,
        lastPagination: null,
        lastSortedPagination: null,
      }
    },
    computed: {
      bysMarkerData() {
        return bysMarkerData.value
      },
      dbName() {
        return DbName.DbMarkerUploadImageHistory
      },
      title() {
        return this.$t('menus.markerUploadImageHistory')
      },
      markerRIDOptions() {
        let options: Array<IControllerOptions> = this.bysMarkerData
          .filter(item => {
            let flag = true
            if (this.orgRID) {
              flag = item.OrgRID === this.orgRID
            }
            return flag
          })
          .filter(item => item.MarkerType === MarkerType.Net4GPro)
          .map((data: bysdb.IDbBysMarker) => {
            return {
              label: data.MarkerNo,
              value: data.RID,
              MarkerHWID: data.MarkerHWID,
              ControllerRID: data.ControllerRID,
              OrgRID: data.OrgRID,
              orderKey: data.MarkerHWID,
              markerType: data.MarkerType,
            }
          })

        options.sort((a, b) => a.orderKey - b.orderKey)

        // 根据输入内容过滤数据
        if (this.deviceFilter) {
          return options.filter(option => {
            const needle = this.deviceFilter.toLowerCase()
            return this.extractSearchMarker ? option.label === needle : option.label.toLowerCase().includes(needle)
            // return option.label.toLowerCase().includes(needle)
          })
        }

        return options
      },
      allImageUrls(): Array<Img> {
        return queryRet.value.map((row) => {
          const MarkerHWID = row.MarkerHWID + ''
          const bysMarker: bysdb.IDbBysMarker | undefined = BysMarker.getDataByIndex(MarkerHWID)
          const MarkerNo = bysMarker?.MarkerNo ?? MarkerHWID
          const typeLabel = this.getCaptureTypeLabel(row)
          // @ts-ignore @ts-nocheck
          const index = row.$index ?? ''
          return {
            src: this.getCameraImageUrl(row),
            title: `${index}: ${MarkerNo} / ${typeLabel}`,
            alt: `${index}: ${MarkerNo} / ${typeLabel} / ${row.UploadTime}`,
          }
        })
      },
      captureType() {
        return {
          1: i18n.global.t('historyTable.sensorCapture'),
          2: i18n.global.t('historyTable.alarmCapture'),
          3: i18n.global.t('historyTable.timerCapture'),
          4: i18n.global.t('historyTable.manualCapture'),
        }
      },
      columns() {
        return [
          {
            name: 'OrgRID',
            field: 'OrgRID',
            label: this.$t('form.unit'),
            sortable: true,
            format: (val: string) => {
              const orgData = Unit.getData(val) as org.IDbOrg | undefined
              return orgData?.ShortName ?? val
            },
          },
          {
            name: 'MarkerHWID', field: 'MarkerHWID', label: this.$t('form.markerName'), sortable: true,
            format: (val: number) => {
              const bysMarker: bysdb.IDbBysMarker | undefined = BysMarker.getDataByIndex(val + '')
              return bysMarker?.MarkerNo ?? val
            },
          },
          {
            name: 'Image', field: (row) => row.FileName, label: this.$t('form.image'),
            sticky: true,
            format: (val: string, row: bysdb.IDbMarkerUploadImageHistory) => {
              return this.getCameraImageUrl(row)
            },
          },
          {
            name: 'CaptureType', field: 'CaptureType', label: this.$t('historyTable.captureType'), sortable: true,
            format: (val: number, row) => this.getCaptureTypeLabel(row),
          },
          { name: 'CaptureTime', field: 'CaptureTime', label: this.$t('historyTable.captureTime'), sortable: true },
          { name: 'UploadTime', field: 'UploadTime', label: this.$t('form.receiptTime'), sortable: true },
          { name: 'Action', field: '', label: '#' },
        ]
      },
      filterColumns() {
        return ['OrgRID', 'MarkerHWID', 'CaptureType', 'CaptureTime', 'UploadTime']
      },
    },
    methods: {
      updatePagination(newPagination) {
        // 只更新lastPagination，不排序
        this.lastPagination = { ...newPagination }
      },
      getSortedRows(rows, pagination) {
        if (!pagination || !pagination.sortBy) return rows.slice()
        const sorted = rows.slice().sort((a, b) => {
          const field = pagination.sortBy
          const dir = pagination.descending ? -1 : 1
          if (a[field] < b[field]) return -1 * dir
          if (a[field] > b[field]) return 1 * dir
          return 0
        })

        return sorted
      },
      getCameraImageUrl(row: bysdb.IDbMarkerUploadImageHistory) {
        return resolveServerPath(`/downloadCameraImage?sys=${this.$store.state.System}&uploadTime=${row.UploadTime}&fileName=${row.FileName}`)
      },
      previewImageWithLightbox(url: string) {
        // 判断排序参数是否变化，只有变化时才排序
        const lastSorted: any = this.lastSortedPagination || {}
        const lastPage: any = this.lastPagination || {}
        const sortChanged =
          lastSorted.sortBy !== lastPage.sortBy ||
          lastSorted.descending !== lastPage.descending
        if (sortChanged && queryRet.value.length > 0) {
          queryRet.value = this.getSortedRows(queryRet.value, lastPage)
          this.lastSortedPagination = { ...lastPage }
        }
        this.$nextTick(() => {
          if (!url) {
            return
          }
          const index = this.allImageUrls.findIndex((item) => item.src === url)
          if (index === -1) {
            return
          }
          this.lightboxIndex = index
          this.visibleLightbox = true
        })
      },
      onHideLightbox() {
        this.visibleLightbox = false
      },
      /**
       * 导出表格数据的额外处理函数
       * @param {Object} source - 表格的一行源数据
       * @param {Object} _row - 完成部分处理的xlsx表格数据
       * @return {Object} 对源数据处理完后保存在_row并返回
       */
      extraExportFunc(source, _row) {
        _row[this.$t('form.image')] = source.FileName
        return _row
      },
      createWhereItems(): crud.IWhereItem[] {
        const where: crud.IWhereItem[] = []

        if (this.orgRID) {
          // 添加单位查询
          const whereItem: crud.IWhereItem = {
            Field: 'OrgRID',
            FieldValue: this.orgRID,
            FieldCompareOperator: '=',
          }
          where.push(whereItem)
        }

        if (this.markerRID) {
          const whereItem: crud.IWhereItem = {
            Field: 'MarkerHWID',
            FieldValue: '' + (this.bysMarkerData.find(item => item.RID === this.markerRID).MarkerHWID ?? ''),
            FieldCompareOperator: '=',
          }
          where.push(whereItem)
        }

        return where
      },
      createOrderByItems(): string[] {
        return [`${this.timeField} asc`]
      },
      createRetColumns(): string[] {
        const obj: Object = new bysdb.DbMarkerUploadImageHistory()
        return getObjAllAttrs(obj)
      },
      getTimeColumn(): string[] {
        return (this.$refs['form-content'] as any).createTimeColumns()
      },
      async onSubmit(where: crud.IWhereItem[]) {
        const queryAlreadyCallBack = function () {
          queryNormal !== true && defaultQueryFinished()
        }
        queryRet.value = []
        queryNormal = false
        this.maxHeight = true
        const orderByItems = this.createOrderByItems()
        const timeColumns = this.getTimeColumn()
        const retColumns = this.createRetColumns()
        const defaultWhereItems = this.createWhereItems().concat(where)
        const submitFunc = (whereItem?: crud.IWhereItem): Promise<boolean> => {
          const promiseDeferred = deferred<boolean>()
          const cancel = QueryBatchV2(
            PrpcDbMarkerUploadImageHistory.QueryBatch,
            {
              Where: whereItem ? defaultWhereItems.concat([whereItem]) : defaultWhereItems,
              OrderBy: orderByItems,
              ResultColumn: retColumns,
              TimeColumn: timeColumns,
            },
            promiseDeferred,
            {
              OnResult: (data) => {
                window.requestIdleCallback(() => {
                  StrPubSub.publish(QueryMarkerUploadImageHistory, data.Rows)
                })
              },
            }
          )
          queryCancelHandlers.push(cancel)
          return promiseDeferred
        }

        let execFns = [submitFunc]
        const promiseAllSets = execFns.map(func => func())
        Promise.all(promiseAllSets)
          .then(queryPromiseRes => {
            queryNormal = queryPromiseRes.find(bool => bool !== true) ?? true
          })
          .finally(() => {
            queryAlreadyCallBack()
          })
      },
      queryDataRet(datas: bysdb.IDbMarkerUploadImageHistory[]) {
        // 为每条数据添加 $index 属性，序号从 1 开始递增
        const startIndex = queryRet.value.length
        const datasWithIndex = datas.map((item, i) => ({
          ...item,
          $index: startIndex + i + 1,
        }))
        queryRet.value = queryRet.value.concat(Object.freeze(datasWithIndex))
        if (this.$refs['form-content']) {
          (this.$refs['form-content'] as any).queryRet = queryRet.value
        }
      },
      clearImageFileContentsCache() {
        // 图片大约400~600kb，如果缓存的图片过多，消耗太多的内存
        // 需要限制下缓存的图片数量，大概在100张，约50M内存大小
        if (UploadImageFileContents.size < 100) {
          return
        }

        // 一次清除最早的10图片缓存
        let i = 10
        const keys = UploadImageFileContents.keys()
        for (const key of keys) {
          UploadImageFileContents.delete(key)
          if (--i <= 0) {
            return
          }
        }
      },
      async fetchCameraImage(row: bysdb.IDbMarkerUploadImageHistory) {
        try {
          const fileName = row.FileName as string
          if (UploadImageFileContents.has(fileName)) {
            return
          }
          // url args: sys, uploadTime, fileName
          const res = await fetch(this.getCameraImageUrl(row))
          if (!res.ok) {
            return
          }
          const imgBlob = await res.blob()
          UploadImageFileContents.set(fileName, imgBlob)
          this.clearImageFileContentsCache()
        } catch (e) {
          log.error('fetchCameraImage catch:', e)
        }
      },
      async downloadCameraImage(row: bysdb.IDbMarkerUploadImageHistory) {
        try {
          const fileName = row.FileName as string
          if (!UploadImageFileContents.has(fileName)) {
            await this.fetchCameraImage(row)
          }
          const imgBlob = UploadImageFileContents.get(fileName)
          if (!imgBlob) {
            this.$q.notify({
              type: 'negative',
              message: this.$t('common.downloadFailed') as string,
            })
            return
          }

          const url = URL.createObjectURL(imgBlob)
          const aEl = document.createElement('a')
          aEl.href = url
          aEl.download = fileName
          aEl.click()
          this.$q.notify({
            type: 'positive',
            message: this.$t('common.downloadSuccess') as string,
          })
          this.$nextTick(() => {
            URL.revokeObjectURL(url)
          })
        } catch (e) {
          log.error('downloadCameraImage catch:', e)
        }
      },
      queryCanceled() {
        for (let i = queryCancelHandlers.length - 1; i >= 0; i--) {
          queryCancelHandlers[i]()
        }
        queryCancelHandlers = []
        queryNormal = false
        this.maxHeight = false
      },

      filterOrgRID(val: string, update: Function) {
        this.orgFilter = val
        update()
      },
      orgRIDChange() {
        this.markerRID = ''
      },

      filterMarkerRID(val: string, update: Function) {
        this.deviceFilter = val
        update()
      },

      getCaptureTypeLabel(row: bysdb.IDbMarkerUploadImageHistory): string {
        try {
          const fromData = JSON.parse(row.FormData ?? '{}')
          const cust_data = JSON.parse(fromData.cust_data ?? '{}')
          if (cust_data.type === undefined || cust_data.type === null) {
            return 'NA'
          }
          return this.captureType[cust_data.type] ?? String(cust_data.type)
        } catch (e) {
          log.info('parse formData error', e)
          return 'NA'
        }
      },
    },
    beforeMount() {
      StrPubSub.subscribe(QueryMarkerUploadImageHistory, this.queryDataRet)
    },
    beforeUnmount() {
      StrPubSub.unsubscribe(QueryMarkerUploadImageHistory, this.queryDataRet)
      UploadImageFileContents.clear()
    },
  })
</script>

<style lang="scss">
  .vel-modal.upload-image-preview-lightbox {
    @apply bg-slate-600;
    top: var(--safe-area-top) !important;
  }

  .marker-history-modal {
    .history-panel {
      height: 100%;

      .history-table {
        &.q-table--dense .q-table {

          th,
          td {
            padding: 2px 4px;
          }
        }

        .sticky-column-last {
          position: sticky !important;
          right: 0;
          background: #fffff3;
        }
      }
    }

    .q-tab-panel {
      padding: unset
    }

    .query-form {

      // 表单有验证条件的下边距为20px，该样式主要同步没有验证条件的表单项样式
      & > *:not(:last-child) {
        padding-bottom: 20px;
      }
    }
  }

  .marker-history-modal:not(.maximized) {
    width: auto !important;
    max-width: unset !important;

    .history-panel .history-table {
      width: 60vw !important;
    }

    .query-form {
      min-width: 400px;
    }
  }

  .popup-proxy-details {
    max-width: 60vw;
  }

  .q-icon.detail-info {
    font-size: 18px;
    border-radius: 4px;
    padding: 0 6px;
    margin: 0 2px;
  }

  .q-icon.detail-info:hover {
    font-size: 18px;
    border-radius: 4px;
    padding: 0 6px;
    margin: 0 2px;
    background-color: $blue-1;
  }
</style>
