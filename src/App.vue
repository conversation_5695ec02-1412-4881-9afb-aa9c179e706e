<template>
  <router-view></router-view>
</template>

<script lang="ts" setup>
  import { onBeforeMount, onMounted, onBeforeUnmount, watch } from 'vue'
  import { isApp } from '@src/config'
  import { useI18n } from 'vue-i18n'
  import { StrPubSub } from 'ypubsub'
  import { FullscreenIsActive } from '@utils/pubSubSubject'
  import { useQuasar, Dialog } from 'quasar'
  import { requirePerms } from '@utils/appPermissions'
  import { deferred } from '@utils/common'
  import { appName } from '@app/package.json'
  import { resolveServerPath } from '@utils/path'
  import log from '@utils/log'
  import semver from 'semver'
  import { safeFileTransfer, safeFilesystem, safeFileViewer } from '@utils/capacitorUtils'

  const $i18n = useI18n()
  const $q = useQuasar()

  watch(() => $q.fullscreen.isActive, (isActive: boolean) => {
    StrPubSub.publish(FullscreenIsActive, isActive)
  })

  // 封装双击返回键，退出app
  function useDeviceready() {
    if (!isApp) {
      return
    }

    // 组件挂载完成Promise
    const mountedDeferred = deferred<boolean>()
    onMounted(() => {
      mountedDeferred.resolve(true)
    })

    // 封装检查升级app
    async function upgradeApp() {
      // 检查APK文件是否已存在并验证文件年龄
      async function checkApkExists(version: string): Promise<string | null> {
        const fileName = `${appName}-${version}.apk`
        const filesystemModule = await safeFilesystem()
        if (!filesystemModule) {
          log.warn('Filesystem module not available')
          return null
        }

        const { Filesystem, Directory } = filesystemModule
        try {
          const stat = await Filesystem.stat({
            path: fileName,
            directory: Directory.Cache,
          })
          // stat.mtime 是 ms 时间戳
          const fileAge = Math.floor((Date.now() - stat.mtime) / (1000 * 60 * 60 * 24))
          if (fileAge > 7) {
            await Filesystem.deleteFile({
              path: fileName,
              directory: Directory.Cache,
            })
            return null
          } else {
            // 返回 Capacitor 路径
            return stat.uri
          }
        } catch (e) {
          // 文件不存在
          return null
        }
      }

      // 后台下载更新文件
      async function downloadInBackground(version: string): Promise<string> {
        const url = resolveServerPath(`/app?filename=${appName}-${version}.apk`)
        const fileName = `${appName}-${version}.apk`

        const filesystemModule = await safeFilesystem()
        const fileTransferModule = await safeFileTransfer()

        if (!filesystemModule || !fileTransferModule) {
          log.warn('Filesystem or FileTransfer module not available')
          throw new Error('Required modules not available')
        }

        const { Filesystem, Directory } = filesystemModule
        const FileTransfer = fileTransferModule

        try {
          // 1. 获取目标文件的 URI
          const fileInfo = await Filesystem.getUri({
            directory: Directory.Cache,
            path: fileName,
          });

          // 2. 用 FileTransfer 下载到该 URI
          const result = await FileTransfer.downloadFile({
            path: fileInfo.uri,
            url,
            progress: false,
            recursive: true,
          })

          return result.path
        } catch (error) {
          log.error('app upgrade download error ', JSON.stringify(error))
          Dialog.create({
            title: $i18n.t('upgradeApp.upgradeFailed') as string,
            message: $i18n.t('upgradeApp.errorMessage', { error: JSON.stringify(error) }) as string,
            class: 'file-path-alarm z-top',
          })
          throw error
        }
      }

      // 安装更新
      async function installApp(fileURL: string) {
        const fileViewerModule = await safeFileViewer()
        if (!fileViewerModule) {
          log.warn('FileViewer module not available')
          return
        }

        const FileViewer = fileViewerModule
        try {
          await FileViewer.openDocumentFromLocalPath({
            path: fileURL, // 本地 APK 路径
          })
        } catch(error) {
          log.error('install app error:', JSON.stringify(error))
          Dialog.create({
            title: $i18n.t('upgradeApp.upgradeFailed') as string,
            message: $i18n.t('upgradeApp.errorMessage', { error: JSON.stringify(error) }) as string,
            class: 'file-path-alarm z-top',
          })
        }
      }

      // 检查更新
      async function checkUpdate() {
        const url = resolveServerPath('/app?filename=versionnumber.txt')
        const response = await fetch(url, {
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
            'Access-Control-Allow-Headers': 'Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization',
          },
        })

        const data = await response.text()
        return data.trim()
      }

      async function run() {
        const version = await checkUpdate()
        if (semver.lte(version, CLIENT_VERSION)) return

        try {
          // 先检查是否已下载
          const existingFileURL = await checkApkExists(version)
          const fileURL = existingFileURL || await downloadInBackground(version)
          Dialog.create({
            message: $i18n.t('upgradeApp.isInstallNewApp', { newVersion: version }) as string,
            class: 'file-path-alarm z-top',
            ok: $i18n.t('upgradeApp.install') as string,
            cancel: $i18n.t('common.cancel') as string,
            persistent: true,
          })
            .onOk(() => {
              installApp(fileURL)
            })
            .onCancel(() => {
              // 用户取消安装，无需处理
            })
            .onDismiss(() => {
              // 对话框关闭时的处理
            })
        } catch (error) {
          log.error('upgrade error', error)
        }
      }

      return run()
    }

    let backButtonLastTime = 0

    // 模拟浏览器历史记录
    function mockHistory() {
      while (window.history.length < 6) {
        window.history.pushState(null, '', '/')
      }
    }

    function exitApp() {
      // 每次取消退出App，重新向history添加记录
      mockHistory()
      // 两次点击返回键时间小于2s，则退出app
      if (Date.now() - backButtonLastTime <= 2000) {
        // 添加到后台运行
        cordova.plugins?.backgroundMode?.moveToBackground()
        navigator.app?.exitApp()
        return
      }

      backButtonLastTime = Date.now()
      $q.notify({
        message: $i18n.t('common.clickAgainExitApp') as string,
        position: 'bottom',
        type: 'info',
        timeout: 2100,
      })
    }

    function backbuttonFn(ev: any) {
      ev.preventDefault()
      ev.stopPropagation()
      exitApp()
    }

    async function deviceready() {
      // 向history添加模拟记录，防止点击返回键浏览器后退，导致路由重载
      mockHistory()
      document.addEventListener('backbutton', backbuttonFn, false)

      await requirePerms()

      // 安全地请求文件系统权限
      const filesystemModule = await safeFilesystem()
      if (filesystemModule) {
        await filesystemModule.Filesystem.requestPermissions()
      }

      await upgradeApp()
    }

    onBeforeMount(() => {
      document.addEventListener('deviceready', deviceready, false)
    })

    onBeforeUnmount(() => {
      document.removeEventListener('backbutton', backbuttonFn)
      document.removeEventListener('deviceready', deviceready)
    })
  }

  useDeviceready()
</script>

<style lang="scss">
  #q-app {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
  }

  .q-dialog.app-exit-dialog {
    font-size: 18px;
    text-align: center;

    .q-card .q-card__actions {
      justify-content: center;
    }
  }

  .file-path-alarm .q-dialog__message {
    word-break: break-word;
  }
</style>
