// Top-level build file where you can add configuration options common to all sub-projects/modules.

// subprojects {
//     afterEvaluate { project ->
//         if (project.hasProperty('android')) {
//             project.android.compileOptions.sourceCompatibility = JavaVersion.VERSION_17
//             project.android.compileOptions.targetCompatibility = JavaVersion.VERSION_17
//         }
//     }
// }

buildscript {

    repositories {
        // 本地仓库，地址是maven本地仓库地址
        mavenLocal()

        // 阿里云仓库
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://maven.aliyun.com/repository/spring' }
        maven { url 'https://maven.aliyun.com/repository/spring-plugin' }
        maven { url 'https://maven.aliyun.com/repository/grails-core' }
        maven { url 'https://maven.aliyun.com/repository/apache-snapshots' }
        maven { url 'https://maven.aliyun.com/repository/mapr-public' }
        maven { url 'https://maven.aliyun.com/repository/releases' }

        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.7.2'
        classpath 'com.google.gms:google-services:4.4.2'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

apply from: "variables.gradle"

allprojects {
    repositories {
        // 本地仓库，地址是maven本地仓库地址
        mavenLocal()

        // 阿里云仓库
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://maven.aliyun.com/repository/spring' }
        maven { url 'https://maven.aliyun.com/repository/spring-plugin' }
        maven { url 'https://maven.aliyun.com/repository/grails-core' }
        maven { url 'https://maven.aliyun.com/repository/apache-snapshots' }
        maven { url 'https://maven.aliyun.com/repository/mapr-public' }
        maven { url 'https://maven.aliyun.com/repository/releases' }

        google()
        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
