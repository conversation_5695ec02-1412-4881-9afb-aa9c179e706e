apply plugin: 'com.android.application'

// 加载自定义签名配置文件
def keyStorePropertiesFile = rootProject.file('../.release-signing.properties')
def keyStoreProperties = new Properties()
keyStoreProperties.load(new FileInputStream(file(keyStorePropertiesFile)))

// 读取自定义 build.properties
def appInfoPropsFile = rootProject.file('./build.properties')
def appInfoProps = new Properties()
appInfoProps.load(new FileInputStream(appInfoPropsFile))
def packageVersionName = appInfoProps.getProperty('VERSION_NAME', '1.0.0')
def packageAppName = appInfoProps.getProperty('APP_NAME', 'bys')

android {
    namespace "beifeng.scenic.system"
    compileSdk rootProject.ext.compileSdkVersion

    defaultConfig {
        applicationId "beifeng.scenic.system"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        aaptOptions {
             // Files and dirs to omit from the packaged assets dir, modified to accommodate modern web apps.
             // Default: https://android.googlesource.com/platform/frameworks/base/+/282e181b58cf72b6ca770dc7ca5f91f135444502/tools/aapt/AaptAssets.cpp#61
            ignoreAssetsPattern '!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~'
        }

        ndk {
            abiFilters 'x86_64', 'x86', 'arm64-v8a', 'armeabi-v7a'
        }
    }

    // 签名的配置
    signingConfigs {
        release {
            storeFile rootProject.file(keyStoreProperties["storeFile"])
            storePassword keyStoreProperties["storePassword"]
            keyAlias keyStoreProperties["keyAlias"]
            keyPassword keyStoreProperties["keyPassword"]
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled false
            // 移除无用的resource文件，必须在minifyEnabled=true的情况下才能使用
            //shrinkResources true
            // 开启zipalign优化
            zipAlignEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    // 设置打包格式
    applicationVariants.all { variant ->
        // 只修改release版本的输出名称
        if (variant.buildType.name == 'release') {
            variant.outputs.all { output ->
                def outputFile = output.outputFile
                if (outputFile != null && outputFile.name.endsWith('.apk')) {
                    // 输出apk名称为bys-v1.0.0.apk
                    output.outputFileName = "${packageAppName}-${packageVersionName}.apk"
                }
            }
        }
    }
}

repositories {
    flatDir{
        dirs '../capacitor-cordova-android-plugins/src/main/libs', 'libs'
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation "androidx.appcompat:appcompat:$androidxAppCompatVersion"
    implementation "androidx.coordinatorlayout:coordinatorlayout:$androidxCoordinatorLayoutVersion"
    implementation "androidx.core:core-splashscreen:$coreSplashScreenVersion"
    implementation project(':capacitor-android')
    testImplementation "junit:junit:$junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$androidxJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$androidxEspressoCoreVersion"
    implementation project(':capacitor-cordova-android-plugins')
}

apply from: 'capacitor.build.gradle'

try {
    def servicesJSON = file('google-services.json')
    if (servicesJSON.text) {
        apply plugin: 'com.google.gms.google-services'
    }
} catch(Exception e) {
    logger.info("google-services.json not found, google-services plugin not applied. Push Notifications won't work")
}
